import Logger from '../../../shared/Logger'

const logger = new Logger({
  prefix: 'DataManager',
  enableColors: true
})

class WebSocketDataManager {
  private static instance: WebSocketDataManager
  private priceData: Map<string, MarketData[]> = new Map()
  private subscribers: DataSubscription[] = []

  constructor() {
    logger.info(`WebSocketDataManager instance created`)
  }

  public static getInstance(): WebSocketDataManager {
    if (!WebSocketDataManager.instance) {
      WebSocketDataManager.instance = new WebSocketDataManager()
    }
    return WebSocketDataManager.instance
  }

  updateMarketData = (data: Omit<MarketData, 'timestamp'>, timestamp?: Date): void => {
    const marketData: MarketData = {
      ...data,
      timestamp: timestamp || new Date()
    }

    if (!this.priceData.has(data.symbol)) {
      this.priceData.set(data.symbol, [])
    }

    this.addPriceData(data.symbol, marketData)
    this.notifySubscribers(marketData)
  }

  subscribe = (subscription: DataSubscription): void => {
    // Check if subscription already exists
    const existingIndex = this.subscribers.findIndex(
      (sub) => sub.id === subscription.id && sub.symbol === subscription.symbol
    )

    if (existingIndex >= 0) {
      // Update existing subscription
      this.subscribers[existingIndex] = subscription
    } else {
      // Add new subscription
      this.subscribers.push(subscription)
    }

    logger.info(`Subscribed to ${subscription.symbol} with id ${subscription.id}`)
  }

  unsubscribe = (id: string, symbol?: string): void => {
    if (symbol) {
      this.subscribers = this.subscribers.filter((sub) => !(sub.id === id && sub.symbol === symbol))
    } else {
      this.subscribers = this.subscribers.filter((sub) => sub.id !== id)
    }

    logger.info(`Unsubscribed ${id} ${symbol ? `from ${symbol}` : 'from all symbols'}`)
  }

  getPriceData = (symbol: string): MarketData[] => {
    return this.priceData.get(symbol) || []
  }

  getLatestPrice = (symbol: string): MarketData | null => {
    const data = this.priceData.get(symbol)
    return data && data.length > 0 ? data[data.length - 1] : null
  }

  private addPriceData = (assetSymbol: string, data: MarketData): void => {
    const priceData = this.priceData.get(assetSymbol) || []

    priceData.push(data)

    if (priceData.length > 1000) {
      priceData.splice(0, priceData.length - 1000)
    }

    this.priceData.set(assetSymbol, priceData)
  }

  private notifySubscribers = (data: MarketData): void => {
    this.subscribers
      .filter((sub) => sub.symbol === data.symbol)
      .forEach((subscription) => {
        try {
          subscription.callback(data)
        } catch (error) {
          logger.error(`Error in subscription callback: ${error}`)
        }
      })
  }

  reset = (): void => {
    this.priceData.clear()
    this.subscribers = []
    logger.info('Data manager reset')
  }
}

export default WebSocketDataManager
