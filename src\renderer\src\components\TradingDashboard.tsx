import { useEffect, useState, useRef } from 'react'
import Input from './ui/Input'
import { Clock } from 'lucide-react'
import { AccountType } from '@renderer/enums'
import LogViewer from './utility/LogViewer'

interface BotStatus {
  isEnabled: boolean
  settings: {
    enabled: boolean
    strategy: string
    selectedAsset: string
    isDemo: AccountType
  }
  session: {
    id: string
    startTime: Date
    endTime?: Date
    isActive: boolean
    totalTrades: number
    profitLoss: number
  } | null
}

const TRADE_DURATION_GRID = [
  ['S5', 'S15', 'S30'],
  ['M1', 'M3', 'M5'],
  ['M30', 'H1', 'H4']
]

export default function TradingDashboard(): React.JSX.Element {
  const [assets, setAssets] = useState<Asset[]>([])
  const [botStatus, setBotStatus] = useState<BotStatus | null>(null)
  const [nextCandleTime, setNextCandleTime] = useState<number | null>(null)
  const [countdown, setCountdown] = useState<string>('--:--')
  const countdownInterval = useRef<NodeJS.Timeout | null>(null)

  const [settings, setSettings] = useState<TradeSettings>({
    tradeCapital: 100,
    targetProfit: 10,
    tradeAmount: 10,
    tradeDuration: 'S30',
    currency: 'USD',
    asset: 'AUDCHZ_otc',
    account: AccountType.Demo
  })

  useEffect(() => {
    const unsub = window.api.on('ws:event', (...args: unknown[]) => {
      const [event, data] = args as [string, unknown]
      if (event === 'assets') {
        setAssets(data as Asset[])
      } else if (event === 'candle:closed') {
        const candleData = data as { candle: Candle; nextCloseTime: number }
        setNextCandleTime(candleData.nextCloseTime)
      }
    })

    // Listen for bot status updates
    const botStartedUnsub = window.api.on('bot:started', () => {
      fetchBotStatus()
    })

    const botStoppedUnsub = window.api.on('bot:stopped', () => {
      fetchBotStatus()
    })

    const botAnalysisUnsub = window.api.on('bot:analysisRun', () => {
      // Optional: Update UI when analysis runs
    })

    // Fetch initial bot status
    fetchBotStatus()

    return () => {
      unsub()
      botStartedUnsub()
      botStoppedUnsub()
      botAnalysisUnsub()
      if (countdownInterval.current) {
        clearInterval(countdownInterval.current)
      }
    }
  }, [])

  useEffect(() => {
    // Update countdown timer
    if (countdownInterval.current) {
      clearInterval(countdownInterval.current)
    }

    if (nextCandleTime) {
      countdownInterval.current = setInterval(() => {
        const now = Date.now()
        const timeLeft = nextCandleTime - now

        if (timeLeft <= 0) {
          setCountdown('00:00')
        } else {
          const minutes = Math.floor(timeLeft / 60000)
          const seconds = Math.floor((timeLeft % 60000) / 1000)
          setCountdown(
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
          )
        }
      }, 100) // Update every 100ms for smooth countdown

      return () => {
        if (countdownInterval.current) {
          clearInterval(countdownInterval.current)
        }
      }
    }
  }, [nextCandleTime])

  const fetchBotStatus = async (): Promise<void> => {
    try {
      const status = await window.api.invoke('bot:getStatus')
      setBotStatus(status as BotStatus)
    } catch (error) {
      console.error('Failed to fetch bot status:', error)
    }
  }

  const handleChange = <K extends keyof TradeSettings>(field: K, value: TradeSettings[K]): void => {
    setSettings((prev) => ({ ...prev, [field]: value }))
  }

  const handleStartBot = (): void => {
    // Update bot settings before starting
    window.api.send('bot:updateSettings', {
      selectedAsset: settings.asset,
      isDemo: settings.account
    })
    window.api.send('bot:start')
  }

  const handleStopBot = (): void => {
    window.api.send('bot:stop')
  }

  const handleTriggerAnalysis = (): void => {
    window.api.send('bot:triggerAnalysis')
  }

  const handleSelectAsset = (asset: string): void => {
    handleChange('asset', asset)
    window.api.send('ws:selectAsset', asset)
  }

  return (
    <div className="w-full p-2 flex">
      <div className="flex flex-col justify-center items-center w-full">
        {/* Candle Countdown Timer */}
        <div className="mb-4 p-3 bg-gray-800 rounded-lg shadow-lg">
          <div className="flex items-center gap-3">
            <Clock size={20} className="text-cyan-500" />
            <div className="text-center">
              <div className="text-xs text-gray-400">Next Candle In</div>
              <div className="text-2xl font-mono font-bold text-white">{countdown}</div>
            </div>
          </div>
        </div>

        <select
          value={settings.asset}
          onChange={(e) => handleSelectAsset(e.target.value)}
          className="mb-3 px-3 py-2 bg-gray-700 text-white rounded"
        >
          {assets &&
            assets.map((asset) => (
              <option key={asset.id} value={asset.symbol}>
                {asset.name}
              </option>
            ))}
        </select>

        <Input
          label="Trade Capital"
          value={settings.tradeCapital.toString()}
          style="inline"
          fontWeight="bold"
          inputWidth={75}
          onChange={(value) => handleChange('tradeCapital', Number(value))}
          placeholder="0.00"
        />
        <Input
          label="Target Profit"
          value={settings.targetProfit.toString()}
          style="inline"
          fontWeight="bold"
          inputWidth={75}
          onChange={(value) => handleChange('targetProfit', Number(value))}
          placeholder="0.00"
        />
        <Input
          label="Trade Amount"
          value={settings.tradeAmount.toString()}
          style="inline"
          fontWeight="bold"
          inputWidth={75}
          onChange={(value) => handleChange('tradeAmount', Number(value))}
          placeholder="0.00"
        />

        <div className="space-y-3">
          <div className="flex items-center gap-2 mt-2 text-right text-gray-200 text-sm font-bold">
            <span className="text-[12px] pl-[17px]">Trade Duration:</span>
            <span className="text-gray-400">
              <Clock size={16} className="text-cyan-700" />
            </span>
          </div>
          <div className="space-y-1 w-[200px]">
            {TRADE_DURATION_GRID.map((row, rowIndex) => (
              <div key={rowIndex} className="grid grid-cols-3 gap-1 px-5">
                {row.map((duration) => (
                  <label
                    key={duration}
                    className={`py-1 text-sm rounded-sm cursor-pointer transition-all duration-200 border flex items-center justify-center ${
                      settings.tradeDuration === duration
                        ? 'active bg-green-600 border-green-500 text-white shadow-lg'
                        : 'bg-gray-700/50 border-gray-600/50 text-gray-300 hover:bg-gray-600/50 hover:border-gray-500'
                    }`}
                  >
                    <input
                      type="radio"
                      name="tradeDuration"
                      value={duration}
                      checked={settings.tradeDuration === duration}
                      onChange={() => handleChange('tradeDuration', duration)}
                      className="sr-only"
                    />
                    {duration}
                  </label>
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* Bot Controls */}
        <div className="mt-6 space-y-3">
          <div className="flex gap-2">
            {!botStatus?.isEnabled ? (
              <button
                className="bg-green-600 hover:bg-green-700 text-white rounded px-4 py-2 font-semibold transition-colors"
                onClick={handleStartBot}
              >
                Start Bot
              </button>
            ) : (
              <button
                className="bg-red-600 hover:bg-red-700 text-white rounded px-4 py-2 font-semibold transition-colors"
                onClick={handleStopBot}
              >
                Stop Bot
              </button>
            )}
            <button
              className="bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2 font-semibold transition-colors"
              onClick={handleTriggerAnalysis}
              disabled={!botStatus?.isEnabled}
            >
              Run Analysis
            </button>
          </div>

          {/* Bot Status Display */}
          {botStatus && (
            <div className="text-sm text-gray-300">
              <div>
                Status:{' '}
                <span className={botStatus.isEnabled ? 'text-green-400' : 'text-red-400'}>
                  {botStatus.isEnabled ? 'Running' : 'Stopped'}
                </span>
              </div>
              {botStatus.session && (
                <div>
                  <div>Session: {botStatus.session.id.slice(-6)}</div>
                  <div>Trades: {botStatus.session.totalTrades}</div>
                  <div>P/L: ${botStatus.session.profitLoss.toFixed(2)}</div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <div className="flex w-full h-full min-w-[400px]">
        <LogViewer />
      </div>
    </div>
  )
}
