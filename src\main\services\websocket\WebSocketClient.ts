import { io, Socket } from 'socket.io-client'
import { AccountType, SocketConnectionState, SocketURLs } from './../../../renderer/src/enums'
import Logger from '../../../shared/Logger'
import { Auth } from '../../../shared/enums'
import { BrowserWindow } from 'electron'
import { formatAssets, formatData } from '../../../shared/utils/parse'
import WebSocketProcessor from './WebSocketProcessor'
import {
  extractTimePeriod,
  getChartTimeframe,
  timePeriodToOffset
} from '../../../shared/utils/helpers'

const logger = new Logger({
  prefix: 'WS',
  enableColors: true
})

class WebSocketClient {
  private static instance: WebSocketClient
  private processor: WebSocketProcessor
  private heartbeatInterval: NodeJS.Timeout | null = null
  private socket: Socket | null = null

  private currentState: SocketConnectionState = SocketConnectionState.DISCONNECTED

  private chartSettings: ChartData = {} as ChartData
  private currentAsset: string = ''
  private balance: number = 0

  constructor() {
    this.processor = WebSocketProcessor.getInstance()
    this.connect()
  }

  public static getInstance(): WebSocketClient {
    if (!WebSocketClient.instance) {
      WebSocketClient.instance = new WebSocketClient()
    }
    return WebSocketClient.instance
  }

  connect(): void {
    if (this.currentState === SocketConnectionState.AUTHENTICATED) return

    logger.info(`Connecting to WebSocket server...`)
    this.currentState = SocketConnectionState.CONNECTING

    const options = {
      transports: ['websocket'],
      query: {
        EIO: '4',
        transport: ['websocket']
      }
    }

    try {
      this.socket = io(SocketURLs.DEMO, {
        ...options,
        extraHeaders: {
          Origin: SocketURLs.ORIGIN
        },
        path: '/socket.io/'
      })

      this.socket.on('connect', () => {
        if (this.socket) {
          this.socket.emit('auth', {
            isDemo: AccountType.Demo,
            isFastHistory: true,
            platform: 2,
            session: Auth.SESSION_ID,
            uid: Auth.USER_ID
          })

          this.setConnectionState(SocketConnectionState.AUTHENTICATING)
        }
      })

      this.socket.on('disconnect', () => {
        this.disconnect()
        this.broadcast(`disconnected`)
      })

      this.setupEventListeners()
    } catch (error) {
      logger.error(`Failed to connect to WebSocket server: ${error}`)
      this.setConnectionState(SocketConnectionState.DISCONNECTED)
    }
  }

  disconnect = (): void => {
    if (this.socket && this.socket.connected) {
      this.stopHeartbeat()
      this.socket.disconnect()
      this.socket = null

      this.setConnectionState(SocketConnectionState.DISCONNECTED)
      logger.warn(`Disconnected from WebSocket server`)
    }
  }

  getAsset = (): string => {
    return this.currentAsset
  }

  getBalance = (): number => {
    return this.balance
  }

  setAsset = (assetSymbol: string): void => {
    if (!assetSymbol) return

    const currentTime = Date.now()
    const timeframe = getChartTimeframe(this.chartSettings)
    const timePeriod = extractTimePeriod(timeframe)
    const offset = timePeriodToOffset(timeframe as string)

    // Clear the history data when changing assets
    if (this.currentAsset && this.currentAsset !== assetSymbol) {
      this.processor.clearHistory()
    }

    this.currentAsset = assetSymbol

    if (this.socket && this.socket.connected) {
      this.socket.emit('changeSymbol', {
        asset: assetSymbol,
        period: timePeriod
      })

      this.socket.emit('loadHistoryPeriod', {
        asset: assetSymbol,
        index: currentTime,
        period: timePeriod,
        offset,
        time: Math.floor(new Date().getTime() / 1000)
      })
    }
  }

  private setupEventListeners = (): void => {
    if (!this.socket) return

    this.socket.on('successauth', this.handleSuccessAuth)
    this.socket.on('successupdateBalance', this.handleBalance)
    this.socket.on('updateCharts', this.handleUpdateCharts)
    this.socket.on('updateHistoryNewFast', this.handleUpdateHistoryNewFast)
    this.socket.on('loadHistoryPeriodFast', this.handleLoadHistoryPeriodFast)
    this.socket.on('updateStream', this.handleUpdateStream)
    this.socket.on('updateAssets', this.handleUpdateAssets)

    this.socket.onAny((event) => {
      logger.info(`Received event: ${event}`)
    })
  }

  private broadcast = (event: string, data?: unknown): void => {
    BrowserWindow.getAllWindows().forEach((win) => {
      if (win && !win.isDestroyed()) {
        if (data) {
          const payload = formatData(data)
          win.webContents.send('ws:event', event, payload)
        } else {
          win.webContents.send('ws:event', event)
        }
      }
    })
  }

  private startHeartbeat = (): void => {
    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.socket) {
        this.socket.emit('ps')
      }
    }, 20000)
  }
  private stopHeartbeat = (): void => {
    if (!this.heartbeatInterval) return

    clearInterval(this.heartbeatInterval)
    this.heartbeatInterval = null
  }

  private setConnectionState = (state: SocketConnectionState): void => {
    this.currentState = state
  }

  // Event Handlers ===============================================

  private handleSuccessAuth = (): void => {
    this.setConnectionState(SocketConnectionState.AUTHENTICATED)
    this.startHeartbeat()

    this.broadcast('connected')
  }

  private handleBalance = (data): void => {
    const formatted = formatData(data) as BalanceData
    this.balance = formatted.balance
    this.broadcast('balance', this.balance)
  }
  /**
   * Currently, we just handle 1 chart for each session
   * @param data
   */
  private handleUpdateCharts = (data: unknown): void => {
    const chartData = formatData(data) as ChartData
    if (Array.isArray(chartData) && chartData.length > 0) {
      this.chartSettings = chartData[0]
      if (typeof this.chartSettings.settings === 'string') {
        this.chartSettings.settings = JSON.parse(this.chartSettings.settings)
      }

      this.currentAsset = this.chartSettings.settings.symbol
      this.broadcast('chartSettings', this.chartSettings)
    }
  }

  private handleUpdateHistoryNewFast = (data: unknown): void => {
    const payload = formatData(data) as HistoryNewFastSettings

    this.processor.processUpdateHistoryNewFast(this.currentAsset, payload)
  }
  private handleLoadHistoryPeriodFast = (data: unknown): void => {
    const payload = formatData(data) as HistoryPeriodSettings

    this.processor.processLoadHistoryPeriodFast(this.currentAsset, payload)
  }

  private handleUpdateStream = (data: unknown): void => {
    const payload = formatData(data) as UpstreamSettings

    this.processor.processUpdateStream(this.currentAsset, this.chartSettings, payload)

    // if (this.isGeneratingSignal) {
    // }
  }

  private handleUpdateAssets = (data: unknown): void => {
    const formatted = formatData(data)
    const payload = formatAssets(formatted)

    const filteredAssets: Asset[] = payload.filter(
      (asset) => asset.category === 'currency' && asset.profit! > 91
    )

    this.broadcast('assets', filteredAssets)
  }
}

export default WebSocketClient
