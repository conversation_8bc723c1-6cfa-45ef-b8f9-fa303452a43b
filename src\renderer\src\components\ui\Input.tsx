import React from 'react'

interface InputProps {
  /** Unique identifier for the input */
  id?: string
  /** Input label text */
  label: string
  /** Current input value */
  value: string | number
  /** Callback function called when input value changes */
  onChange?: (value: string) => void
  /** Input type */
  type?: 'text' | 'number' | 'email' | 'password' | 'tel' | 'url'
  /** Placeholder text (used in placeholder style or as fallback) */
  placeholder?: string
  /** Whether the input is disabled */
  disabled?: boolean
  /** Whether the input is required */
  required?: boolean
  /** Additional CSS classes to apply */
  className?: string
  /** Font weight */
  fontWeight?: 'normal' | 'medium' | 'bold'
  /** Layout style option */
  style?: 'inline' | 'stacked' | 'placeholder'
  /** Size variant */
  size?: 'small' | 'medium' | 'large'
  /** Visual variant */
  variant?: 'default' | 'error' | 'success' | 'warning'
  /** Error message to display */
  inputTextColor?: string
  inputWidth?: number
  labelWidth?: number
  error?: string
  /** Helper text to display */
  helperText?: string
  /** Icon to display next to the label (for inline style) */
  icon?: React.ReactNode
  /** Minimum value (for number inputs) */
  min?: number | string
  /** Maximum value (for number inputs) */
  max?: number | string
  /** Step value (for number inputs) */
  step?: number | string
}

const Input: React.FC<InputProps> = ({
  id,
  label,
  value,
  onChange,
  type = 'text',
  placeholder,
  disabled = false,
  required = false,
  className = '',
  style = 'stacked',
  size = 'medium',
  variant = 'default',
  fontWeight = 'normal',
  error,
  helperText,
  icon,
  min,
  max,
  step,
  inputWidth,
  labelWidth
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (onChange) {
      onChange(e.target.value)
    }
  }

  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
  const hasError = Boolean(error)
  const effectivePlaceholder = placeholder || (style === 'placeholder' ? label : undefined)

  // Container classes based on style
  const getContainerClasses = (): string => {
    const baseClasses = 'flex font-sans'

    const styleClasses = {
      inline: 'flex-row items-center gap-2 mb-1',
      stacked: 'flex-col',
      placeholder: 'flex-col'
    }

    return `${baseClasses} ${styleClasses[style]} ${className}`
  }

  // Input classes based on size, variant, and state
  const getInputClasses = (): string => {
    // Base input classes
    const classes = [
      'bg-[#0D1117]',
      'border',
      'rounded-sm',
      'text-gray-100', // #f0f6fc
      'transition-all',
      'duration-150',
      'ease-in-out',
      'outline-none',
      'min-w-[50px]',
      'font-inherit'
    ]

    // Size variants
    const sizeClasses = {
      small: 'py-1 px-2 text-xs',
      medium: 'py-1.5 px-2.5 text-xs',
      large: 'py-2.5 px-3 text-sm'
    }
    classes.push(sizeClasses[size])

    // Border and focus states
    if (hasError) {
      classes.push(
        'border-red-500',
        'focus:border-red-500',
        'focus:shadow-[0_0_0_2px_rgba(248,81,73,0.1)]'
      )
    } else if (variant === 'success') {
      classes.push(
        'border-green-600',
        'focus:border-green-600',
        'focus:shadow-[0_0_0_1px_rgba(46,160,67,0.1)]'
      )
    } else if (variant === 'warning') {
      classes.push(
        'border-orange-500',
        'focus:border-orange-500',
        'focus:shadow-[0_0_0_1px_rgba(251,133,0,0.1)]'
      )
    } else {
      classes.push(
        'border-gray-700', // #30363d
        'focus:border-green-600', // #2ea043
        'focus:shadow-[0_0_0_1px_rgba(46,160,67,0.1)]',
        'hover:border-gray-600' // #6e7681
      )
    }

    // Disabled state
    if (disabled) {
      classes.push(
        'bg-gray-800', // #21262d
        'border-gray-700', // #30363d
        'text-gray-500', // #6e7681
        'cursor-not-allowed'
      )
    }

    // Type-specific styling
    if (type === 'number') {
      classes.push(
        '[appearance:textfield]',
        '[&::-webkit-outer-spin-button]:appearance-none',
        '[&::-webkit-inner-spin-button]:appearance-none'
      )
    }

    if (type === 'password') {
      classes.push('font-mono', 'tracking-wider')
    }

    // Icon padding
    if (icon) {
      classes.push('pl-5')
    }

    return classes.join(' ')
  }

  // Label classes
  const getLabelClasses = (): string => {
    const baseClasses = 'text-gray-200 text-xs cursor-pointer select-none'

    const fontWeightClasses = {
      normal: 'font-normal',
      medium: 'font-medium',
      bold: 'font-bold'
    }

    const sizeClasses = {
      small: 'text-xs',
      medium: 'text-xs',
      large: 'text-sm'
    }

    let classes = `${baseClasses} ${fontWeightClasses[fontWeight]} ${sizeClasses[size]}`

    if (style === 'inline') {
      classes += ' flex-shrink-0'
    } else if (style === 'stacked') {
      classes += ' mb-1'
    }

    if (disabled) {
      classes += ' text-gray-500'
    }

    return classes
  }

  const renderInput = (): React.JSX.Element => {
    const inputElement = (
      <input
        id={inputId}
        type={type}
        value={value}
        onChange={handleChange}
        placeholder={effectivePlaceholder}
        disabled={disabled}
        required={required}
        className={`${getInputClasses()} placeholder:text-gray-500 placeholder:opacity-100 focus:placeholder:text-gray-400`}
        min={min}
        max={max}
        step={step}
        style={inputWidth ? { width: `${inputWidth}px`, flex: 'none' } : { width: '100%' }}
      />
    )

    if (!icon) {
      return inputElement
    }

    return (
      <div className="flex items-center relative">
        <div className="absolute left-1 top-1/2 transform -translate-y-1/2 text-green-700 z-10">
          {icon}
        </div>
        {inputElement}
      </div>
    )
  }

  const renderLabel = (): React.JSX.Element | null => {
    if (style === 'placeholder') return null

    return (
      <label
        htmlFor={inputId}
        className={`${getLabelClasses()} ${style === 'inline' ? 'text-right flex items-center' : ''}`}
        style={
          labelWidth
            ? { width: `${labelWidth}px` }
            : style === 'inline'
              ? { minWidth: '84px' }
              : undefined
        }
      >
        {label}
        {required && <span className="text-red-500 ml-0.5">*</span>}
        {style === 'stacked' && ':'}
      </label>
    )
  }

  const renderError = (): React.JSX.Element | null => {
    if (!error) return null
    return (
      <span
        className={`text-xs text-red-500 mt-1 block ${style === 'inline' ? 'ml-[calc(84px+8px)]' : ''}`}
      >
        {error}
      </span>
    )
  }

  const renderHelperText = (): React.JSX.Element | null => {
    if (!helperText || error) return null
    return (
      <span
        className={`text-xs text-gray-500 mt-1 block ${style === 'inline' ? 'ml-[calc(84px+8px)]' : ''}`}
      >
        {helperText}
      </span>
    )
  }

  return (
    <div className={getContainerClasses()}>
      {renderLabel()}
      {renderInput()}
      {renderError()}
      {renderHelperText()}
    </div>
  )
}

Input.displayName = 'Input'

export default Input
