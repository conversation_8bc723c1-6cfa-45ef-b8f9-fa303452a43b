import { BrowserWindow } from 'electron'
import Logger from '../../shared/Logger'
import { AccountType } from '../../renderer/src/enums'
import WebSocketClient from './websocket/WebSocketClient'
import RiskManager from './RiskManager'
import Analyzer from './Analyzer'
import WebSocketProcessor from './websocket/WebSocketProcessor'

interface AutoTraderSettings {
  enabled: boolean
  strategy: string
  selectedAsset: string
  isDemo: AccountType
}

const logger = new Logger({
  prefix: 'TRADER',
  enableColors: true
})

class AutoTrader {
  private static instance: AutoTrader
  private isAuto: boolean = false
  private settings: AutoTraderSettings
  private riskSettings: RiskManager
  private session: TradingSession | null = null
  private wsClient: WebSocketClient | null = null
  private analyzer: Analyzer | null = null
  private wsProcessor: WebSocketProcessor | null = null
  private analysisTimer: NodeJS.Timeout | null = null
  private analysisInterval: number = 5000 // 5 seconds default

  constructor(
    settings: Partial<AutoTraderSettings> = {},
    riskSettings: Partial<RiskSettings> = {}
  ) {
    logger.info(`AutoTrader instance created`)
    this.sendLog(`🤖 AutoTrader instance created`)

    this.settings = {
      ...settings,
      enabled: false,
      strategy: 'rsi-bollinger',
      selectedAsset: 'AUDCHZ_otc',
      isDemo: AccountType.Demo
    }

    this.riskSettings = new RiskManager(riskSettings)
    this.wsProcessor = WebSocketProcessor.getInstance()
  }

  public static getInstance(): AutoTrader {
    if (!AutoTrader.instance) {
      AutoTrader.instance = new AutoTrader()
    }
    return AutoTrader.instance
  }

  initialize(client: WebSocketClient): void {
    this.wsClient = client

    const balance = this.wsClient.getBalance()
    if (balance) {
      this.riskSettings.initializeSession(balance)
    }

    // Initialize analyzer with processor data
    if (this.wsProcessor) {
      this.analyzer = new Analyzer({
        marketData: this.wsProcessor.getMarketData(),
        chartSettings: {} as ChartData,
        selectedAsset: this.settings.selectedAsset
      })
    }
  }

  isEnabled(): boolean {
    return this.isAuto
  }

  enable = (): void => {
    this.isAuto = true
    logger.info('AutoTrader enabled')
    this.sendLog('✅ AutoTrader enabled')
  }

  disable = (): void => {
    this.isAuto = false
    logger.info('AutoTrader disabled')
    this.sendLog('🛑 AutoTrader disabled')
  }

  start(): void {
    // Do not start if not connected
    if (!this.wsClient) {
      logger.error('Cannot start AutoTrader: WebSocket client not initialized')
      this.sendLog('❌ Cannot start: WebSocket not connected')
      return
    }

    this.enable()
    this.startTradingSession()
    this.startAnalysis()

    logger.info('AutoTrader started successfully')
    this.sendLog('🚀 AutoTrader started successfully')

    // Notify UI that bot has started
    this.broadcast('bot:started', {
      settings: this.settings,
      session: this.session
    })
  }

  stop(): void {
    this.disable()
    this.stopAnalysis()

    if (this.session) {
      this.session.endTime = new Date()
      this.session.isActive = false

      // Calculate session stats
      const duration =
        (this.session.endTime.getTime() - this.session.startTime.getTime()) / 1000 / 60
      this.sendLog(
        `📊 Session ended - Duration: ${duration.toFixed(1)} min, Trades: ${this.session.totalTrades}, P/L: $${this.session.profitLoss.toFixed(2)}`
      )
    }

    logger.info('AutoTrader stopped')
    this.sendLog('⏹️ AutoTrader stopped')

    // Notify UI that bot has stopped
    this.broadcast('bot:stopped', {
      session: this.session
    })
  }

  updateSettings(newSettings: Partial<AutoTraderSettings>): void {
    const isEnabled = this.settings.enabled
    this.settings = { ...this.settings, ...newSettings }

    // Update analyzer if asset changed
    if (newSettings.selectedAsset && this.analyzer && this.wsProcessor) {
      this.analyzer = new Analyzer({
        marketData: this.wsProcessor.getMarketData(),
        chartSettings: {} as ChartData,
        selectedAsset: this.settings.selectedAsset
      })
    }

    // Restart if it was enabled before
    if (isEnabled && this.isAuto) {
      this.stop()
      this.start()
    }

    this.sendLog(`⚙️ Settings updated: ${JSON.stringify(newSettings)}`)
  }

  getSettings(): AutoTraderSettings {
    return { ...this.settings }
  }

  processAnalysisResults = async (selectedAsset: string, data: AnalysisData): Promise<void> => {
    if (!this.isAuto || !this.wsClient || !this.session?.isActive) return

    try {
      // Check risk management constraints
      const canTrade = await this.riskSettings.canTrade({
        symbol: selectedAsset,
        amount: this.riskSettings.getTradeAmount(),
        signal: 'BUY' // This will be determined by analysis
      })

      if (!canTrade) {
        logger.warn('Risk management prevented trade execution')
        this.sendLog('⚠️ Trade blocked by risk management')
        return
      }

      // Process the analysis data and generate signals
      const currentPrice = data[data.length - 1]?.price
      if (!currentPrice) return

      // Here you would implement your trading logic based on analysis
      logger.info(`Processing analysis for ${selectedAsset} at price ${currentPrice}`)
      this.sendLog(`📊 Analysis complete for ${selectedAsset} - Price: $${currentPrice}`)

      // Update session stats
      this.session.totalTrades++

      // Broadcast analysis results
      this.broadcast('bot:analysis', {
        asset: selectedAsset,
        price: currentPrice,
        timestamp: new Date(),
        session: this.session
      })
    } catch (error) {
      logger.error(`Error processing analysis results: ${error}`)
      this.sendLog(`❌ Analysis error: ${error}`)
    }
  }

  performMarketAnalysis = async (selectedAsset: string, data: AnalysisData): Promise<void> => {
    if (!this.isAuto || !data || data.length === 0) return

    try {
      logger.info(`Performing market analysis for ${selectedAsset}`)

      // Process the analysis through the trading strategy
      await this.processAnalysisResults(selectedAsset, data)
    } catch (error) {
      logger.error(`Market analysis failed: ${error}`)
      this.sendLog(`❌ Market analysis failed: ${error}`)
    }
  }

  private sendLog(message: string): void {
    BrowserWindow.getAllWindows().forEach((win) => {
      if (win && !win.isDestroyed()) {
        win.webContents.send('event:log', message)
      }
    })
  }

  private broadcast(event: string, data?: unknown): void {
    BrowserWindow.getAllWindows().forEach((win) => {
      if (win && !win.isDestroyed()) {
        win.webContents.send(event, data)
      }
    })
  }

  private startAnalysis = (): void => {
    if (!this.analyzer) {
      logger.error('Cannot start analysis: Analyzer not initialized')
      return
    }

    // Clear any existing timer
    this.stopAnalysis()

    // Subscribe to candle close events
    if (this.wsProcessor) {
      this.wsProcessor.onCandleClose((candle) => {
        logger.info('Candle closed, triggering analysis')
        this.sendLog(
          `🕯️ Candle closed at ${new Date(candle.timestamp * 1000).toLocaleTimeString()}`
        )
        this.runAnalysis()
      })
    }

    // Also run analysis on timer
    this.analysisTimer = setInterval(() => {
      logger.info('Timer triggered analysis')
      this.runAnalysis()
    }, this.analysisInterval)

    logger.info(`Analysis started with ${this.analysisInterval}ms interval`)
    this.sendLog(`🔄 Analysis loop started (${this.analysisInterval / 1000}s interval)`)
  }

  private stopAnalysis = (): void => {
    if (this.analysisTimer) {
      clearInterval(this.analysisTimer)
      this.analysisTimer = null
    }

    // Unsubscribe from candle events
    if (this.wsProcessor) {
      this.wsProcessor.offCandleClose()
    }

    logger.info('Analysis stopped')
  }

  private runAnalysis = async (): Promise<void> => {
    if (!this.analyzer || !this.isAuto) return

    try {
      await this.analyzer.runAnalysis()

      // Broadcast that analysis was run
      this.broadcast('bot:analysisRun', {
        timestamp: new Date(),
        asset: this.settings.selectedAsset
      })
    } catch (error) {
      logger.error(`Analysis run failed: ${error}`)
      this.sendLog(`❌ Analysis failed: ${error}`)
    }
  }

  private startTradingSession = (): void => {
    this.session = {
      id: `session_${Date.now()}`,
      startTime: new Date(),
      isActive: true,
      totalTrades: 0,
      profitLoss: 0
    }

    this.sendLog(`📈 New trading session started: ${this.session.id}`)
  }

  // Public method to manually trigger analysis
  triggerAnalysis = (): void => {
    if (!this.analyzer) {
      logger.error('Cannot trigger analysis: Analyzer not initialized')
      this.sendLog('❌ Cannot run analysis: Bot not properly initialized')
      return
    }

    logger.info('Manual analysis triggered')
    this.sendLog('🔍 Manual analysis triggered')
    this.runManualAnalysis()
  }

  private runManualAnalysis = async (): Promise<void> => {
    if (!this.analyzer) return

    try {
      await this.analyzer.runAnalysis()

      // Broadcast that analysis was run
      this.broadcast('bot:analysisRun', {
        timestamp: new Date(),
        asset: this.settings.selectedAsset
      })
    } catch (error) {
      logger.error(`Manual analysis failed: ${error}`)
      this.sendLog(`❌ Manual analysis failed: ${error}`)
    }
  }

  // Get current session stats
  getSessionStats = (): TradingSession | null => {
    return this.session
  }

  // Set analysis interval
  setAnalysisInterval = (intervalMs: number): void => {
    this.analysisInterval = Math.max(1000, intervalMs) // Minimum 1 second

    if (this.isAuto && this.analysisTimer) {
      // Restart with new interval
      this.stopAnalysis()
      this.startAnalysis()
    }

    this.sendLog(`⏱️ Analysis interval set to ${this.analysisInterval / 1000}s`)
  }
}

export default AutoTrader
