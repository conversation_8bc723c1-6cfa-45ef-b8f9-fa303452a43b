import RSIIndicator from './indicators/rsi/RSIIndicator'
import Logger from '../../shared/Logger'

const logger = new Logger({
  prefix: 'TA',
  enableColors: true
})

export interface TechnicalAnalysisResult {
  timestamp: Date
  symbol: string
  currentPrice: number

  // Individual indicator results
  rsi: RSIResult | null
  bollingerBands: BollingerBandsResult | null
  movingAverage: MovingAverageResult | null
  volumeAnalysis: VolumeAnalysisResult | null
  macd: MACDResult | null

  // Combined analysis
  overallSignal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
  confidence: number
  signalStrength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG'

  // Signal breakdown
  bullishSignals: number
  bearishSignals: number
  neutralSignals: number

  // Risk assessment
  volatility: 'LOW' | 'MODERATE' | 'HIGH' | 'EXTREME'
  trend: 'STRONG_UPTREND' | 'UPTREND' | 'SIDEWAYS' | 'DOWNTREND' | 'STRONG_DOWNTREND'

  // Trading recommendations
  entryPrice?: number
  stopLoss?: number
  takeProfit?: number
  positionSize?: number

  // Additional insights
  marketCondition: 'TRENDING' | 'RANGING' | 'VOLATILE' | 'BREAKOUT'
  supportLevel?: number
  resistanceLevel?: number
}

// Placeholder interfaces for missing indicators
interface RSIResult {
  value: number
  signal: 'OVERBOUGHT' | 'NEUTRAL' | 'OVERSOLD'
  strength: number
}

interface BollingerBandsResult {
  upperBand: number
  middleBand: number
  lowerBand: number
  percentB: number
  bandwidth: number
}

interface MovingAverageResult {
  shortSMA: number
  longSMA: number
  ema: number
  crossoverType?: 'GOLDEN_CROSS' | 'DEATH_CROSS' | null
}

interface VolumeAnalysisResult {
  average: number
  current: number
  ratio: number
  trend: 'INCREASING' | 'DECREASING' | 'STABLE'
}

interface MACDResult {
  macd: number
  signal: number
  histogram: number
  crossover?: 'BULLISH' | 'BEARISH' | null
}

class TechnicalAnalysis {
  private static instance: TechnicalAnalysis
  private priceHistory: number[] = []

  private rsiIndicator: RSIIndicator

  constructor() {
    this.rsiIndicator = RSIIndicator.getInstance()
  }

  public static getInstance(): TechnicalAnalysis {
    if (!TechnicalAnalysis.instance) {
      TechnicalAnalysis.instance = new TechnicalAnalysis()
    }
    return TechnicalAnalysis.instance
  }

  analyze(marketData: MarketData[]): TechnicalAnalysisResult | null {
    if (marketData.length <= 20) return null

    this.priceHistory = marketData.map((data) => data.price)

    const currentData = marketData[marketData.length - 1]

    // Calculate individual indicators
    const rsiResult = this.calculateRSI(this.priceHistory)
    const bollingerResult = this.calculateBollingerBands(this.priceHistory)
    const movingAverageResult = this.calculateMovingAverages(this.priceHistory)
    const macdResult = this.calculateMACD(this.priceHistory)

    // Combine individual indicators
    const combinedAnalysis = this.combineAnalysis({
      rsiResult,
      bollingerResult,
      movingAverageResult,
      macdResult
    })

    const volatility = this.assessVolatility(bollingerResult)
    const trend = this.assessTrend(movingAverageResult)

    // Calculate support and resistance levels
    const { support, resistance } = this.calculateSupportResistanceLevel(this.priceHistory)

    const tradingRecommendations = this.generateTradingRecommendations({
      price: currentData.price,
      analysis: combinedAnalysis,
      volatility,
      support,
      resistance
    })

    const result: TechnicalAnalysisResult = {
      timestamp: currentData.timestamp,
      symbol: currentData.symbol,
      currentPrice: currentData.price,
      rsi: rsiResult,
      bollingerBands: bollingerResult,
      movingAverage: movingAverageResult,
      volumeAnalysis: null,
      macd: macdResult,
      ...combinedAnalysis,
      volatility,
      trend,
      marketCondition: this.determineMarketCondition(volatility, trend),
      supportLevel: support,
      resistanceLevel: resistance,
      ...tradingRecommendations
    }

    return result
  }

  private calculateRSI(prices: number[]): RSIResult | null {
    if (prices.length < 14) return null

    const rsiValue = this.rsiIndicator.calculate(prices)
    if (!rsiValue) return null

    return {
      value: rsiValue.value,
      signal: rsiValue.value >= 70 ? 'OVERBOUGHT' : rsiValue.value <= 30 ? 'OVERSOLD' : 'NEUTRAL',
      strength: rsiValue.value >= 80 || rsiValue.value <= 20 ? 1 : 0.5
    }
  }

  private calculateBollingerBands(
    prices: number[],
    period: number = 20,
    stdDev: number = 2
  ): BollingerBandsResult | null {
    if (prices.length < period) return null

    const recentPrices = prices.slice(-period)
    const sma = recentPrices.reduce((sum, price) => sum + price, 0) / period

    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period
    const standardDeviation = Math.sqrt(variance)

    const upperBand = sma + stdDev * standardDeviation
    const lowerBand = sma - stdDev * standardDeviation
    const currentPrice = prices[prices.length - 1]

    const percentB = (currentPrice - lowerBand) / (upperBand - lowerBand)
    const bandwidth = (upperBand - lowerBand) / sma

    return {
      upperBand,
      middleBand: sma,
      lowerBand,
      percentB,
      bandwidth
    }
  }

  private calculateMovingAverages(prices: number[]): MovingAverageResult | null {
    if (prices.length < 50) return null

    const shortPeriod = 20
    const longPeriod = 50

    // Simple Moving Averages
    const shortSMA = prices.slice(-shortPeriod).reduce((sum, price) => sum + price, 0) / shortPeriod
    const longSMA = prices.slice(-longPeriod).reduce((sum, price) => sum + price, 0) / longPeriod

    // Exponential Moving Average (20 period)
    const multiplier = 2 / (shortPeriod + 1)
    let ema = prices[prices.length - shortPeriod]

    for (let i = prices.length - shortPeriod + 1; i < prices.length; i++) {
      ema = (prices[i] - ema) * multiplier + ema
    }

    // Check for crossovers
    let crossoverType: 'GOLDEN_CROSS' | 'DEATH_CROSS' | null = null
    if (prices.length >= longPeriod + 1) {
      const prevShortSMA =
        prices.slice(-shortPeriod - 1, -1).reduce((sum, price) => sum + price, 0) / shortPeriod
      const prevLongSMA =
        prices.slice(-longPeriod - 1, -1).reduce((sum, price) => sum + price, 0) / longPeriod

      if (prevShortSMA <= prevLongSMA && shortSMA > longSMA) {
        crossoverType = 'GOLDEN_CROSS'
      } else if (prevShortSMA >= prevLongSMA && shortSMA < longSMA) {
        crossoverType = 'DEATH_CROSS'
      }
    }

    return {
      shortSMA,
      longSMA,
      ema,
      crossoverType
    }
  }

  private calculateMACD(prices: number[]): MACDResult | null {
    if (prices.length < 26) return null

    // Calculate EMAs
    const ema12 = this.calculateEMA(prices, 12)
    const ema26 = this.calculateEMA(prices, 26)

    if (!ema12 || !ema26) return null

    const macdLine = ema12 - ema26

    // Calculate signal line (9-period EMA of MACD)
    const macdHistory: number[] = []
    for (let i = 26; i <= prices.length; i++) {
      const subPrices = prices.slice(0, i)
      const subEma12 = this.calculateEMA(subPrices, 12)
      const subEma26 = this.calculateEMA(subPrices, 26)
      if (subEma12 && subEma26) {
        macdHistory.push(subEma12 - subEma26)
      }
    }

    const signalLine = this.calculateEMAFromValues(macdHistory, 9)
    const histogram = macdLine - signalLine

    // Check for crossovers
    let crossover: 'BULLISH' | 'BEARISH' | null = null
    if (macdHistory.length >= 2) {
      const prevMacd = macdHistory[macdHistory.length - 2]
      const prevSignal = this.calculateEMAFromValues(macdHistory.slice(0, -1), 9)

      if (prevMacd <= prevSignal && macdLine > signalLine) {
        crossover = 'BULLISH'
      } else if (prevMacd >= prevSignal && macdLine < signalLine) {
        crossover = 'BEARISH'
      }
    }

    return {
      macd: macdLine,
      signal: signalLine,
      histogram,
      crossover
    }
  }

  private calculateEMA(prices: number[], period: number): number | null {
    if (prices.length < period) return null

    const multiplier = 2 / (period + 1)
    let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period

    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] - ema) * multiplier + ema
    }

    return ema
  }

  private calculateEMAFromValues(values: number[], period: number): number {
    if (values.length < period) return values[values.length - 1]

    const multiplier = 2 / (period + 1)
    let ema = values.slice(0, period).reduce((sum, val) => sum + val, 0) / period

    for (let i = period; i < values.length; i++) {
      ema = (values[i] - ema) * multiplier + ema
    }

    return ema
  }

  private combineAnalysis(indicators: {
    rsiResult: RSIResult | null
    bollingerResult: BollingerBandsResult | null
    movingAverageResult: MovingAverageResult | null
    macdResult: MACDResult | null
  }): {
    overallSignal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
    confidence: number
    signalStrength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG'
    bullishSignals: number
    bearishSignals: number
    neutralSignals: number
  } {
    let bullishSignals = 0
    let bearishSignals = 0
    let neutralSignals = 0
    let totalWeight = 0
    let weightedScore = 0

    // RSI Analysis
    if (indicators.rsiResult) {
      const weight = 0.25
      totalWeight += weight

      if (indicators.rsiResult.signal === 'OVERSOLD') {
        bullishSignals++
        weightedScore += weight * 0.8
      } else if (indicators.rsiResult.signal === 'OVERBOUGHT') {
        bearishSignals++
        weightedScore -= weight * 0.8
      } else {
        neutralSignals++
      }
    }

    // Bollinger Bands Analysis
    if (indicators.bollingerResult) {
      const weight = 0.25
      totalWeight += weight

      if (indicators.bollingerResult.percentB < 0.2) {
        bullishSignals++
        weightedScore += weight * 0.7
      } else if (indicators.bollingerResult.percentB > 0.8) {
        bearishSignals++
        weightedScore -= weight * 0.7
      } else {
        neutralSignals++
      }
    }

    // Moving Average Analysis
    if (indicators.movingAverageResult) {
      const weight = 0.3
      totalWeight += weight

      if (indicators.movingAverageResult.crossoverType === 'GOLDEN_CROSS') {
        bullishSignals++
        weightedScore += weight * 1.0
      } else if (indicators.movingAverageResult.crossoverType === 'DEATH_CROSS') {
        bearishSignals++
        weightedScore -= weight * 1.0
      } else if (indicators.movingAverageResult.shortSMA > indicators.movingAverageResult.longSMA) {
        bullishSignals++
        weightedScore += weight * 0.5
      } else if (indicators.movingAverageResult.shortSMA < indicators.movingAverageResult.longSMA) {
        bearishSignals++
        weightedScore -= weight * 0.5
      } else {
        neutralSignals++
      }
    }

    // MACD Analysis
    if (indicators.macdResult) {
      const weight = 0.2
      totalWeight += weight

      if (indicators.macdResult.crossover === 'BULLISH') {
        bullishSignals++
        weightedScore += weight * 0.9
      } else if (indicators.macdResult.crossover === 'BEARISH') {
        bearishSignals++
        weightedScore -= weight * 0.9
      } else if (indicators.macdResult.histogram > 0) {
        bullishSignals++
        weightedScore += weight * 0.4
      } else if (indicators.macdResult.histogram < 0) {
        bearishSignals++
        weightedScore -= weight * 0.4
      } else {
        neutralSignals++
      }
    }

    // Normalize the score
    const normalizedScore = totalWeight > 0 ? weightedScore / totalWeight : 0
    const confidence = Math.abs(normalizedScore)

    // Determine overall signal
    let overallSignal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' = 'NEUTRAL'
    let signalStrength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG' = 'WEAK'

    if (normalizedScore >= 0.7) {
      overallSignal = 'STRONG_BUY'
      signalStrength = 'VERY_STRONG'
    } else if (normalizedScore >= 0.4) {
      overallSignal = 'BUY'
      signalStrength = 'STRONG'
    } else if (normalizedScore <= -0.7) {
      overallSignal = 'STRONG_SELL'
      signalStrength = 'VERY_STRONG'
    } else if (normalizedScore <= -0.4) {
      overallSignal = 'SELL'
      signalStrength = 'STRONG'
    } else if (Math.abs(normalizedScore) >= 0.2) {
      signalStrength = 'MODERATE'
    }

    return {
      overallSignal,
      confidence,
      signalStrength,
      bullishSignals,
      bearishSignals,
      neutralSignals
    }
  }

  private assessVolatility(
    bollingerBands: BollingerBandsResult | null
  ): 'LOW' | 'MODERATE' | 'HIGH' | 'EXTREME' {
    if (!bollingerBands) return 'MODERATE'

    const bandwidth = bollingerBands.bandwidth

    if (bandwidth < 0.01) return 'LOW'
    if (bandwidth < 0.02) return 'MODERATE'
    if (bandwidth < 0.04) return 'HIGH'
    return 'EXTREME'
  }

  private assessTrend(
    movingAverages: MovingAverageResult | null
  ): 'STRONG_UPTREND' | 'UPTREND' | 'SIDEWAYS' | 'DOWNTREND' | 'STRONG_DOWNTREND' {
    if (!movingAverages) return 'SIDEWAYS'

    const { shortSMA, longSMA } = movingAverages
    const difference = ((shortSMA - longSMA) / longSMA) * 100

    if (difference > 2) return 'STRONG_UPTREND'
    if (difference > 0.5) return 'UPTREND'
    if (difference < -2) return 'STRONG_DOWNTREND'
    if (difference < -0.5) return 'DOWNTREND'
    return 'SIDEWAYS'
  }

  private calculateSupportResistanceLevel(prices: number[]): {
    support: number
    resistance: number
  } {
    if (prices.length < 20) {
      const currentPrice = prices[prices.length - 1]
      return {
        support: currentPrice * 0.98,
        resistance: currentPrice * 1.02
      }
    }

    const recentPrices = prices.slice(-50)
    const pivotPoints: number[] = []

    // Find local minima and maxima
    for (let i = 2; i < recentPrices.length - 2; i++) {
      const prev2 = recentPrices[i - 2]
      const prev1 = recentPrices[i - 1]
      const current = recentPrices[i]
      const next1 = recentPrices[i + 1]
      const next2 = recentPrices[i + 2]

      // Local maximum
      if (current > prev1 && current > prev2 && current > next1 && current > next2) {
        pivotPoints.push(current)
      }
      // Local minimum
      else if (current < prev1 && current < prev2 && current < next1 && current < next2) {
        pivotPoints.push(current)
      }
    }

    const currentPrice = prices[prices.length - 1]
    const supportLevels = pivotPoints.filter((p) => p < currentPrice).sort((a, b) => b - a)
    const resistanceLevels = pivotPoints.filter((p) => p > currentPrice).sort((a, b) => a - b)

    return {
      support: supportLevels[0] || currentPrice * 0.98,
      resistance: resistanceLevels[0] || currentPrice * 1.02
    }
  }

  private generateTradingRecommendations(params: {
    price: number
    analysis: {
      overallSignal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
      confidence: number
      signalStrength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG'
      bullishSignals: number
      bearishSignals: number
      neutralSignals: number
    }
    volatility: 'LOW' | 'MODERATE' | 'HIGH' | 'EXTREME'
    support: number
    resistance: number
  }): {
    entryPrice?: number
    stopLoss?: number
    takeProfit?: number
    positionSize?: number
  } {
    const { price, analysis, volatility, support, resistance } = params

    if (analysis.overallSignal === 'NEUTRAL' || analysis.confidence < 0.5) {
      return {}
    }

    const isLong = analysis.overallSignal === 'BUY' || analysis.overallSignal === 'STRONG_BUY'

    // Calculate risk based on volatility
    const riskMultiplier =
      volatility === 'LOW' ? 0.5 : volatility === 'MODERATE' ? 1 : volatility === 'HIGH' ? 1.5 : 2
    const baseRisk = 0.02 // 2% base risk

    const entryPrice = price
    const stopLoss = isLong
      ? Math.max(support, price * (1 - baseRisk * riskMultiplier))
      : Math.min(resistance, price * (1 + baseRisk * riskMultiplier))

    const riskRewardRatio = 2 // 1:2 risk/reward
    const takeProfitDistance = Math.abs(price - stopLoss) * riskRewardRatio
    const takeProfit = isLong ? price + takeProfitDistance : price - takeProfitDistance

    // Position sizing based on confidence
    const positionSize = analysis.confidence

    return {
      entryPrice,
      stopLoss,
      takeProfit,
      positionSize
    }
  }

  private determineMarketCondition(
    volatility: 'LOW' | 'MODERATE' | 'HIGH' | 'EXTREME',
    trend: 'STRONG_UPTREND' | 'UPTREND' | 'SIDEWAYS' | 'DOWNTREND' | 'STRONG_DOWNTREND'
  ): 'TRENDING' | 'RANGING' | 'VOLATILE' | 'BREAKOUT' {
    if (volatility === 'EXTREME') return 'VOLATILE'
    if (volatility === 'HIGH' && (trend === 'STRONG_UPTREND' || trend === 'STRONG_DOWNTREND'))
      return 'BREAKOUT'
    if (trend === 'SIDEWAYS') return 'RANGING'
    return 'TRENDING'
  }

  reset(): void {
    this.priceHistory = []
    logger.info('Technical analysis reset')
  }
}

export default TechnicalAnalysis
