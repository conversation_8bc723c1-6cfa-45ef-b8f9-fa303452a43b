import { BrowserWindow } from 'electron'
import AutoTrader from './AutoTrader'
import SignalGenerator from './SignalGenerator'
import TechnicalAnalysis from './TechnicalAnalysis'
import StrategyManager from './StrategyManager'
import Logger from '../../shared/Logger'

interface AnalyzerSettings {
  marketData: Map<string, MarketDataResult>
  chartSettings: ChartData
  selectedAsset?: string
}

const logger = new Logger({
  prefix: 'Analyzer',
  enableColors: true
})

class Analyzer {
  private marketData: Map<string, MarketDataResult> = new Map()
  private chartSettings: ChartData
  private selectedAsset: string
  private autoTrader: AutoTrader
  private technicalAnalysisEngine: TechnicalAnalysis
  private signalGenerator: SignalGenerator
  private strategyManager: StrategyManager

  constructor({ marketData, chartSettings, selectedAsset }: AnalyzerSettings) {
    this.marketData = marketData
    this.autoTrader = AutoTrader.getInstance()

    this.chartSettings = chartSettings
    this.selectedAsset = selectedAsset || ''

    this.technicalAnalysisEngine = TechnicalAnalysis.getInstance()
    this.signalGenerator = new SignalGenerator()
    this.strategyManager = new StrategyManager()
  }
  runAnalysis = async (): Promise<void> => {
    let assetsToAnalyze: string[] = []

    if (this.autoTrader && this.autoTrader.isEnabled()) {
      const autoTraderSettings = this.autoTrader.getSettings()

      // If no asset is selected, analyze any asset from marketData
      if (autoTraderSettings && !autoTraderSettings.selectedAsset) {
        // Get the current asset from chartSettings
        if (this.chartSettings && this.chartSettings.settings.symbol) {
          assetsToAnalyze.push(this.chartSettings.settings.symbol)
        }
      } else if (autoTraderSettings && autoTraderSettings.selectedAsset) {
        assetsToAnalyze = [autoTraderSettings.selectedAsset]
      }

      // Include the selected asset from the UI if there is any.
      if (this.selectedAsset && !assetsToAnalyze.includes(this.selectedAsset)) {
        assetsToAnalyze.push(this.selectedAsset)
      }

      // Fallback to analyze the selected asset from the UI if there is any.
      if (assetsToAnalyze.length === 0 && this.selectedAsset) {
        assetsToAnalyze = [this.selectedAsset]
      }

      for (const selectedAsset of assetsToAnalyze) {
        const marketData = this.marketData.get(selectedAsset)
        if (marketData && marketData.candles.length >= 20) {
          const analysisData = marketData.candles.map((candle) => ({
            symbol: selectedAsset,
            price: candle.close,
            timestamp: new Date(candle.timestamp * 1000)
          }))

          if (this.autoTrader && this.autoTrader.isEnabled()) {
            await this.autoTrader.performMarketAnalysis(selectedAsset, analysisData)
          }

          // Run manual analysis from selected asset in the UI
          if (selectedAsset === this.selectedAsset) {
            const analysis = this.technicalAnalysisEngine.analyze(analysisData)
            if (!analysis) continue

            // Run strategy analysis
            const strategyResult = this.strategyManager.analyze(analysisData)
            if (!strategyResult) continue

            // Generate advanced signal for UI display
            const advancedSignal = this.signalGenerator.generateAdvanced(analysis)

            // Update the last analysis timestamp
            marketData.lastAnalysis = new Date()

            // Send the analysis results to the renderer
            BrowserWindow.getAllWindows().forEach((win) => {
              if (win && !win.isDestroyed()) {
                win.webContents.send('analysis:complete', {
                  symbol: selectedAsset,
                  analysis,
                  strategyResult,
                  advancedSignal,
                  currentPrice:
                    marketData.latestTick?.price ||
                    marketData.candles[marketData.candles.length - 1].close,
                  candleCount: marketData.candles.length
                })
              }
            })

            if (strategyResult.primarySignal && strategyResult.confidence >= 0.6) {
              const signal = strategyResult.primarySignal
              const strengthText = this.getSignalStrengthText(strategyResult.confidence)
              const signalIcon = this.getSignalIcon(strategyResult.confidence)
              logger.sendToLogView(
                `${signalIcon} ${strengthText} ${signal.signal} Signal for ${selectedAsset} | Confidence: ${(strategyResult.confidence * 100).toFixed(1)}% | Strength: ${signal.strength}`
              )
            }

            // Send manual signal recommendation for high confidence signals
            if (
              strategyResult.primarySignal &&
              strategyResult.confidence >= 0.8 &&
              !this.autoTrader.isEnabled()
            ) {
              BrowserWindow.getAllWindows().forEach((win) => {
                if (win && !win.isDestroyed()) {
                  win.webContents.send('analysis:signal', {
                    symbol: selectedAsset,
                    signal: strategyResult.primarySignal!.signal,
                    confidence: strategyResult.confidence,
                    reasoning: strategyResult.primarySignal!.reasoning,
                    recommendedAmount: strategyResult.recommendedAmount,
                    recommendedExpiration: strategyResult.recommendedExpiration,
                    currentPrice:
                      marketData.latestTick?.price ||
                      marketData.candles[marketData.candles.length - 1]?.close,
                    timestamp: new Date()
                  })
                }
              })
            }
          }
        }
      }
    }
  }

  private getSignalStrengthText(confidence: number): string {
    if (confidence >= 0.9) return 'EXTREMELY STRONG'
    if (confidence >= 0.8) return 'VERY STRONG'
    if (confidence >= 0.7) return 'STRONG'
    if (confidence >= 0.6) return 'MODERATE'
    return 'WEAK'
  }

  private getSignalIcon(confidence: number): string {
    if (confidence >= 0.9) return '🚀' // Rocket for extremely strong
    if (confidence >= 0.8) return '🔥' // Fire for very strong
    if (confidence >= 0.7) return '🎯' // Target for strong
    if (confidence >= 0.6) return '📊' // Chart for moderate
    return '🤔' // Thinking for weak
  }
}

export default Analyzer
