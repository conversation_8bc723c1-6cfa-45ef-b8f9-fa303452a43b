import { TechnicalAnalysisResult } from './TechnicalAnalysis'
import Logger from '../../shared/Logger'

const logger = new Logger({
  prefix: 'SIGNAL',
  enableColors: true
})

interface AdvancedSignal {
  timestamp: Date
  primarySignal: 'BUY' | 'SELL' | 'NEUTRAL'
  patterns: Pattern[]
  confidence: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  entryConditions: string[]
  exitConditions: string[]
  stopLossLevel?: number
  takeProfitLevel?: number
  expectedDuration: number // in seconds
}

interface Pattern {
  name: string
  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL'
  confidence: number
  description: string
}

class SignalGenerator {
  private signalHistory: AdvancedSignal[] = []

  generateAdvanced(analysis: TechnicalAnalysisResult): AdvancedSignal | null {
    if (!analysis) return null

    const patterns = this.detectPatterns(
      analysis.rsi,
      analysis.bollingerBands,
      analysis.movingAverage,
      analysis.volumeAnalysis,
      analysis.currentPrice
    )

    if (patterns.length === 0) {
      logger.warn(
        `No patterns detected. Using base technical analysis confidence: ${analysis.confidence}`
      )
    } else {
      logger.info(`Patterns detected: ${patterns.map((p) => p.name).join(', ')}`)
    }

    const primarySignal = this.determinePrimarySignal(patterns, analysis.overallSignal)
    // Fallback: if no patterns, use analysis.confidence directly
    const confidence =
      patterns.length === 0
        ? analysis.confidence
        : this.calculateOverallConfidence(patterns, analysis.confidence)
    const riskLevel = this.assessRiskLevel(analysis.volatility, patterns)

    const signal: AdvancedSignal = {
      timestamp: new Date(),
      primarySignal,
      patterns,
      confidence,
      riskLevel,
      entryConditions: this.generateEntryConditions(patterns, analysis),
      exitConditions: this.generateExitConditions(patterns, analysis),
      stopLossLevel: analysis.stopLoss,
      takeProfitLevel: analysis.takeProfit,
      expectedDuration: this.determineExpectedDuration(patterns)
    }

    this.signalHistory.push(signal)

    // Keep only last 50 signals
    if (this.signalHistory.length > 50) {
      this.signalHistory = this.signalHistory.slice(-50)
    }

    return signal
  }

  private detectPatterns(
    rsi: { value: number; signal: string; strength: number } | null,
    bollingerBands: {
      upperBand: number
      middleBand: number
      lowerBand: number
      percentB: number
      bandwidth: number
    } | null,
    movingAverage: {
      shortSMA: number
      longSMA: number
      ema: number
      crossoverType?: 'GOLDEN_CROSS' | 'DEATH_CROSS' | null
    } | null,
    _volumeAnalysis: unknown,
    _currentPrice: number
  ): Pattern[] {
    const patterns: Pattern[] = []

    // RSI Divergence Pattern
    if (rsi) {
      if (rsi.value < 30) {
        patterns.push({
          name: 'RSI Oversold',
          type: 'BULLISH',
          confidence: 0.7,
          description: 'RSI indicates oversold conditions'
        })
      } else if (rsi.value > 70) {
        patterns.push({
          name: 'RSI Overbought',
          type: 'BEARISH',
          confidence: 0.7,
          description: 'RSI indicates overbought conditions'
        })
      }
    }

    // Bollinger Band Squeeze
    if (bollingerBands) {
      if (bollingerBands.bandwidth < 0.02) {
        patterns.push({
          name: 'Bollinger Band Squeeze',
          type: 'NEUTRAL',
          confidence: 0.6,
          description: 'Volatility contraction detected, potential breakout ahead'
        })
      }

      if (bollingerBands.percentB > 1) {
        patterns.push({
          name: 'Price Above Upper Band',
          type: 'BEARISH',
          confidence: 0.65,
          description: 'Price trading above upper Bollinger Band'
        })
      } else if (bollingerBands.percentB < 0) {
        patterns.push({
          name: 'Price Below Lower Band',
          type: 'BULLISH',
          confidence: 0.65,
          description: 'Price trading below lower Bollinger Band'
        })
      }
    }

    // Moving Average Patterns
    if (movingAverage) {
      if (movingAverage.crossoverType === 'GOLDEN_CROSS') {
        patterns.push({
          name: 'Golden Cross',
          type: 'BULLISH',
          confidence: 0.85,
          description: 'Short MA crossed above long MA'
        })
      } else if (movingAverage.crossoverType === 'DEATH_CROSS') {
        patterns.push({
          name: 'Death Cross',
          type: 'BEARISH',
          confidence: 0.85,
          description: 'Short MA crossed below long MA'
        })
      }
    }

    return patterns
  }

  private determinePrimarySignal(
    patterns: Pattern[],
    overallSignal: string
  ): 'BUY' | 'SELL' | 'NEUTRAL' {
    if (patterns.length === 0) {
      return overallSignal === 'STRONG_BUY' || overallSignal === 'BUY'
        ? 'BUY'
        : overallSignal === 'STRONG_SELL' || overallSignal === 'SELL'
          ? 'SELL'
          : 'NEUTRAL'
    }

    const bullishPatterns = patterns.filter((p) => p.type === 'BULLISH')
    const bearishPatterns = patterns.filter((p) => p.type === 'BEARISH')

    const bullishScore = bullishPatterns.reduce((sum, p) => sum + p.confidence, 0)
    const bearishScore = bearishPatterns.reduce((sum, p) => sum + p.confidence, 0)

    if (bullishScore > bearishScore && bullishScore > 0.5) {
      return 'BUY'
    } else if (bearishScore > bullishScore && bearishScore > 0.5) {
      return 'SELL'
    }

    return 'NEUTRAL'
  }

  private calculateOverallConfidence(patterns: Pattern[], baseConfidence: number): number {
    if (patterns.length === 0) return baseConfidence

    const patternConfidence = patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length
    return (baseConfidence + patternConfidence) / 2
  }

  private assessRiskLevel(volatility: string, patterns: Pattern[]): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (volatility === 'EXTREME' || volatility === 'HIGH') {
      return 'HIGH'
    }

    const conflictingPatterns =
      patterns.filter((p) => p.type === 'BULLISH').length > 0 &&
      patterns.filter((p) => p.type === 'BEARISH').length > 0

    if (conflictingPatterns) {
      return 'HIGH'
    }

    if (volatility === 'LOW') {
      return 'LOW'
    }

    return 'MEDIUM'
  }

  private generateEntryConditions(
    patterns: Pattern[],
    analysis: TechnicalAnalysisResult
  ): string[] {
    const conditions: string[] = []

    if (analysis.overallSignal === 'BUY' || analysis.overallSignal === 'STRONG_BUY') {
      conditions.push(`Price confirms above ${analysis.currentPrice.toFixed(5)}`)
      if (analysis.movingAverage) {
        conditions.push(`Short MA (${analysis.movingAverage.shortSMA.toFixed(5)}) above Long MA`)
      }
    } else if (analysis.overallSignal === 'SELL' || analysis.overallSignal === 'STRONG_SELL') {
      conditions.push(`Price confirms below ${analysis.currentPrice.toFixed(5)}`)
      if (analysis.movingAverage) {
        conditions.push(`Short MA (${analysis.movingAverage.shortSMA.toFixed(5)}) below Long MA`)
      }
    }

    patterns.forEach((pattern) => {
      if (pattern.confidence > 0.7) {
        conditions.push(pattern.description)
      }
    })

    return conditions
  }

  private generateExitConditions(
    _patterns: Pattern[],
    analysis: TechnicalAnalysisResult
  ): string[] {
    const conditions: string[] = []

    if (analysis.stopLoss) {
      conditions.push(`Stop Loss: ${analysis.stopLoss.toFixed(5)}`)
    }
    if (analysis.takeProfit) {
      conditions.push(`Take Profit: ${analysis.takeProfit.toFixed(5)}`)
    }

    if (analysis.rsi) {
      if (analysis.overallSignal === 'BUY' || analysis.overallSignal === 'STRONG_BUY') {
        conditions.push('RSI reaches overbought (>70)')
      } else if (analysis.overallSignal === 'SELL' || analysis.overallSignal === 'STRONG_SELL') {
        conditions.push('RSI reaches oversold (<30)')
      }
    }

    conditions.push('Signal reversal detected')
    conditions.push('Maximum holding time reached')

    return conditions
  }

  private determineExpectedDuration(patterns: Pattern[]): number {
    // Base duration in seconds
    let duration = 60 // 1 minute default

    const hasStrongPattern = patterns.some((p) => p.confidence > 0.8)
    const hasCrossoverPattern = patterns.some(
      (p) => p.name === 'Golden Cross' || p.name === 'Death Cross'
    )

    if (hasCrossoverPattern) {
      duration = 300 // 5 minutes for crossover patterns
    } else if (hasStrongPattern) {
      duration = 180 // 3 minutes for strong patterns
    }

    return duration
  }

  reset(): void {
    this.signalHistory = []
    logger.info('Signal generator reset')
  }

  getSignalHistory(): AdvancedSignal[] {
    return [...this.signalHistory]
  }
}

export default SignalGenerator
