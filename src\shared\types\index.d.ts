import { AccountType } from '../../renderer/src/enums'

declare global {
  interface UpstreamSettings extends Array<[asset: string, timestamp: number, price: number]> {}

  interface BalanceData {
    isDemo: AccountType
    balance: number
  }

  interface ChartData {
    chart_id: string
    settings: ChartSettings
  }

  interface ChartSettings {
    chartId: string
    chartType: number
    chartPeriod: number
    candlesTimer: boolean
    symbol: string
    demoDealAmount: number
    liveDealAmount: number
    enabledTradeMonitor: boolean
    enabledRatingWidget: boolean
    isVisible: boolean
    fastTimeframe: number
    enabledAutoscroll: boolean
    enabledGridSnap: boolean
    minimizedTradePanel: boolean
    fastCloseAt: number
    enableQuickAutoOffset: boolean
    quickAutoOffsetValue: number
    showArea: boolean
    percentAmount: number
  }

  interface Asset {
    /** 0 → Unique numerical identifier of the asset */
    id?: number
    /** 1 → Trading symbol, e.g. `#AAPL` */
    symbol: string
    /** 2 → Human-readable name */
    name?: string
    /** 3 → Asset class/category (`currency`, `stock`, `index`, …) */
    category?: string
    /** 4 → Internal group identifier used by the platform */
    group?: number
    /** 5 → Current payout / profit percentage offered (e.g. 80 ⇒ 80 %) */
    profit?: number
    /** 6 → Minimum trade duration that can be selected (seconds) */
    minTime?: number
    /** 7 → Maximum trade duration that can be selected (seconds) */
    maxTime?: number
    /** 8 → Price precision (number of digits after the decimal separator) */
    precision?: number
    /** 9 → 1 if the symbol is OTC, 0 otherwise */
    isOTC?: number
    /** 10 → Identifier of the OTC twin (if regular market asset) */
    otcId?: number
    /** 11 → Identifier of the regular-market twin (if OTC asset) */
    regularId?: number
    /** 12 → Array with detailed trading schedule information */
    schedule?: unknown[]
    /** 13 → Timestamp when the current schedule starts (Unix seconds) */
    scheduleStart?: number
    /** 14 → Whether the asset is currently active/tradable */
    isActive?: boolean
    /** 15 → List of timeframes the asset supports on the chart */
    timeframes?: AssetTimeframe[]
    /** 16 → Timestamp when the current schedule ends (Unix seconds) */
    scheduleEnd?: number
    /** 17 → Minimum amount allowed per trade */
    minAmount?: number
    /** 18 → Maximum amount allowed per trade */
    maxAmount?: number
  }

  interface RiskSettings {
    // Capital Management
    maxDailyRisk: number // Percentage of balance
    riskPerTrade: number // Percentage of balance per trade
    maxConcurrentTrades: number
    maxExposurePerAsset: number // Percentage of balance

    // Profit/Loss Management
    dailyProfitTarget: number // Dollar amount or percentage
    dailyLossLimit: number // Dollar amount or percentage
    sessionProfitTarget: number
    sessionLossLimit: number

    // Stacking/Martingale
    enableMartingale: boolean
    martingaleMultiplier: number // 2.0 = double on loss
    maxMartingaleSteps: number
    martingaleResetOnWin: boolean

    // Time Management
    minTimeBetweenTrades: number // Seconds
    cooldownAfterLoss: number // Seconds
    respectExpirationTimes: boolean
    maxTradesPerHour: number

    // Position Sizing
    minTradeAmount: number
    maxTradeAmount: number
    dynamicSizing: boolean
  }

  interface SessionStats {
    startBalance: number
    currentBalance: number
    totalTrades: number
    winTrades: number
    lossTrades: number
    drawTrades: number
    totalProfit: number
    dailyProfit: number
    winRate: number
    lastTradeTime: Date | null
    consecutiveLosses: number
    consecutiveWins: number
    tradesThisHour: number
    hourlyTradeReset: Date
  }

  interface MarketData {
    symbol: string
    price: number
    timestamp: Date
    bid?: number
    ask?: number
  }

  interface MarketDataResult {
    symbol: string
    candles: Candle[]
    latestTick: TickData | null
    lastAnalysis: Date | null
  }

  interface Candle {
    timestamp: number
    open: number
    high: number
    low: number
    close: number
  }

  interface TickData {
    asset: string
    timestamp: number
    price: number
  }

  interface DataSubscription {
    id: string
    symbol: string
    callback: (data: MarketData) => void
  }

  interface TradingSession {
    id: string
    startTime: Date
    endTime?: Date
    isActive: boolean
    totalTrades: number
    profitLoss: number
  }

  type StrategyType = 'rsi-bollinger' | 'ma-crossover' | 'breakout' | 'momentum'

  interface StrategyResult {
    signals: StrategySignal[]
    primarySignal: StrategySignal | null
    confidence: number
    shouldTrade: boolean
    recommendedAmount?: number
    recommendedExpiration?: number
  }

  interface StrategySignal {
    strategy: StrategyType
    signal: 'BUY' | 'SELL' | 'NEUTRAL'
    confidence: number
    strength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG'
    timestamp: Date
    symbol: string
    currentPrice: number
    reasoning: string[]
    entryConditions: string[]
    exitConditions?: string[]
  }

  type AnalysisData = MarketData[]

  interface TradeRequest {
    symbol: string
    amount: number
    signal: 'BUY' | 'SELL'
  }
}

export {}
