type RawAssetTuple = [
  number, // id
  string, // symbol
  string, // name
  string, // category
  number, // group
  number, // profit
  number, // minTime
  number, // maxTime
  number, // precision
  number, // isOTC
  number, // otcId
  number, // regularId
  unknown[], // schedule
  number, // scheduleStart
  unknown, // isActive
  unknown[], // timeframes
  number, // scheduleEnd
  number, // minAmount
  number // maxAmount
]

export const formatData = (data: unknown): unknown | null => {
  try {
    if (data instanceof Buffer || data instanceof ArrayBuffer) {
      const buffer = data as Buffer
      const jsonStr = buffer.toString('utf-8')
      return JSON.parse(jsonStr)
    }

    if (typeof data === 'string') {
      try {
        return JSON.parse(data)
      } catch {
        return data
      }
    }

    return data
  } catch {
    return null
  }
}

export const formatAssets = (data: unknown): Asset[] => {
  try {
    if (Array.isArray(data) && data.length > 0 && data[0] instanceof Array) {
      return (data as RawAssetTuple[]).map(
        (item) =>
          ({
            /** 0 → Unique numerical identifier of the asset */
            id: item[0],
            /** 1 → Trading symbol, e.g. `#AAPL` */
            symbol: item[1],
            /** 2 → Human-readable name */
            name: item[2],
            /** 3 → Asset class/category (`currency`, `stock`, `index`, …) */
            category: item[3],
            /** 4 → Internal group identifier used by the platform */
            group: item[4],
            /** 5 → Current payout / profit percentage offered (e.g. 80 ⇒ 80 %) */
            profit: item[5],
            /** 6 → Minimum trade duration that can be selected (seconds) */
            minTime: item[6],
            /** 7 → Maximum trade duration that can be selected (seconds) */
            maxTime: item[7],
            /** 8 → Price precision (number of digits after the decimal separator) */
            precision: item[8],
            /** 9 → 1 if the symbol is OTC, 0 otherwise */
            isOTC: item[9],
            /** 10 → Identifier of the OTC twin (if regular market asset) */
            otcId: item[10],
            /** 11 → Identifier of the regular-market twin (if OTC asset) */
            regularId: item[11],
            /** 12 → Array with detailed trading schedule information */
            schedule: item[12],
            /** 13 → Timestamp when the current schedule starts (Unix seconds) */
            scheduleStart: item[13],
            /** 14 → Whether the asset is currently active/tradable */
            isActive: Boolean(item[14]),
            /** 15 → List of timeframes the asset supports on the chart */
            timeframes: item[15],
            /** 16 → Timestamp when the current schedule ends (Unix seconds) */
            scheduleEnd: item[16],
            /** 17 → Minimum amount allowed per trade */
            minAmount: item[17],
            /** 18 → Maximum amount allowed per trade */
            maxAmount: item[18]
          }) as Asset
      )
    }

    return data as Asset[]
  } catch {
    return []
  }
}
