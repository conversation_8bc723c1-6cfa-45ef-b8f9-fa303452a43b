import pc from 'picocolors'
import { writeFileSync, appendFileSync, existsSync, mkdirSync, statSync, readFileSync } from 'fs'
import { join } from 'path'
import { BrowserWindow } from 'electron'

enum LoggerLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  SUCCESS = 3,
  WARN = 4,
  ERROR = 5
}

const LoggerLevelNames = {
  [LoggerLevel.TRACE]: 'TRACE',
  [LoggerLevel.DEBUG]: 'DEBUG',
  [LoggerLevel.INFO]: 'INFO',
  [LoggerLevel.SUCCESS]: 'SUCCESS',
  [LoggerLevel.WARN]: 'WARN',
  [LoggerLevel.ERROR]: 'ERROR'
} as const

interface LoggerSettings {
  showLogLevel: boolean
  showTimestamp: boolean
  prefix?: string
  minLevel?: LoggerLevel
  outputFile?: string
  maxFileSize?: number // in MB
  enableColors?: boolean
  dateFormat?: string
}

type LogData = Record<string, unknown>

interface LogEntry {
  timestamp: string
  level: LoggerLevel
  message: string
  data?: LogData
  context?: string
}

class Logger {
  private static instance: Logger
  private settings: Required<LoggerSettings>
  private logBuffer: LogEntry[] = []
  private fileSize: number = 0

  constructor(settings: Partial<LoggerSettings> = {}) {
    this.settings = {
      showLogLevel: true,
      showTimestamp: true,
      prefix: '',
      minLevel: LoggerLevel.INFO,
      outputFile: '',
      maxFileSize: 10, // 10MB default
      enableColors: true,
      dateFormat: 'ISO',
      ...settings
    }

    this.initializeFileLogging()
  }

  public static getInstance(settings?: Partial<LoggerSettings>): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(
        settings || {
          showLogLevel: true,
          showTimestamp: true,
          minLevel: LoggerLevel.INFO,
          enableColors: true
        }
      )
    }
    return Logger.instance
  }

  private initializeFileLogging(): void {
    if (this.settings.outputFile) {
      try {
        const dir = join(process.cwd(), 'logs')
        if (!existsSync(dir)) {
          mkdirSync(dir, { recursive: true })
        }

        const filePath = join(dir, this.settings.outputFile)
        if (existsSync(filePath)) {
          const stats = statSync(filePath)
          this.fileSize = stats.size / (1024 * 1024) // Convert to MB
        }
      } catch (error) {
        console.error('Failed to initialize file logging:', error)
      }
    }
  }

  // Enhanced logging methods with proper level handling
  trace(message: string, data?: LogData, context?: string): void {
    this.logWithLevel(LoggerLevel.TRACE, message, data, context)
  }

  debug(message: string, data?: LogData, context?: string): void {
    this.logWithLevel(LoggerLevel.DEBUG, message, data, context)
  }

  info(message: string, data?: LogData, context?: string): void {
    this.logWithLevel(LoggerLevel.INFO, message, data, context)
  }

  success(message: string, data?: LogData, context?: string): void {
    this.logWithLevel(LoggerLevel.SUCCESS, message, data, context)
  }

  warn(message: string, data?: LogData, context?: string): void {
    this.logWithLevel(LoggerLevel.WARN, message, data, context)
  }

  error(message: string, data?: LogData, context?: string): void {
    this.logWithLevel(LoggerLevel.ERROR, message, data, context)
  }

  // Trading-specific logging methods
  trade(action: string, symbol: string, data?: LogData): void {
    this.logWithLevel(LoggerLevel.INFO, `TRADE: ${action}`, { symbol, ...data }, 'TRADING')
  }

  performance(operation: string, duration: number, data?: LogData): void {
    this.logWithLevel(
      LoggerLevel.DEBUG,
      `PERF: ${operation}`,
      { duration_ms: duration, ...data },
      'PERFORMANCE'
    )
  }

  audit(action: string, userId?: string, data?: LogData): void {
    this.logWithLevel(
      LoggerLevel.INFO,
      `AUDIT: ${action}`,
      { userId, timestamp: new Date().toISOString(), ...data },
      'AUDIT'
    )
  }

  security(event: string, data?: LogData): void {
    this.logWithLevel(
      LoggerLevel.WARN,
      `SECURITY: ${event}`,
      this.maskSensitiveData(data),
      'SECURITY'
    )
  }

  sendToLogView(message: string): void {
    const windows = BrowserWindow.getAllWindows()

    if (windows && windows.length > 0) {
      windows.forEach((window) => {
        if (window && !window.isDestroyed() && window.webContents) {
          window.webContents.send('event:log', message)
        }
      })
    }
  }

  // Core logging method with level filtering
  private logWithLevel(
    level: LoggerLevel,
    message: string,
    data?: LogData,
    context?: string
  ): void {
    // Check if log level meets minimum threshold
    if (level < this.settings.minLevel) {
      return
    }

    const logEntry: LogEntry = {
      timestamp: this.formatTimestamp(),
      level,
      message,
      data,
      context
    }

    // Add to buffer for potential file output
    this.logBuffer.push(logEntry)

    // Console output
    console.log(this.formatConsoleMessage(logEntry))

    // File output if configured
    if (this.settings.outputFile) {
      this.writeToFile(logEntry)
    }

    // Trim buffer if it gets too large
    if (this.logBuffer.length > 1000) {
      this.logBuffer = this.logBuffer.slice(-500)
    }
  }

  private formatTimestamp(): string {
    const now = new Date()
    switch (this.settings.dateFormat) {
      case 'ISO':
        return now.toISOString()
      case 'locale':
        return now.toLocaleString()
      case 'time':
        return now.toLocaleTimeString()
      default:
        return now.toISOString()
    }
  }

  private formatConsoleMessage(entry: LogEntry): string {
    const parts: string[] = []

    // Timestamp
    if (this.settings.showTimestamp) {
      const timestampColor = this.settings.enableColors
        ? pc.gray(`${entry.timestamp}`)
        : entry.timestamp
      parts.push(timestampColor)
    }

    // Log level with colors
    if (this.settings.showLogLevel) {
      const levelName = LoggerLevelNames[entry.level]
      const coloredLevel = this.settings.enableColors
        ? this.colorizeLevel(` [${levelName}]`, entry.level)
        : levelName
      parts.push(coloredLevel)
    }

    // Prefix
    if (this.settings.prefix) {
      const coloredPrefix = this.settings.enableColors
        ? pc.cyan(`[${this.settings.prefix}]`)
        : this.settings.prefix
      parts.push(coloredPrefix)
    }

    // Context
    if (entry.context) {
      const coloredContext = this.settings.enableColors
        ? pc.magenta(`[${entry.context}]`)
        : entry.context
      parts.push(coloredContext)
    }

    // Message
    const coloredMessage = this.settings.enableColors
      ? this.colorizeMessage(` ${entry.message}`, entry.level)
      : ` ${entry.message}`
    parts.push(coloredMessage)

    // Data
    if (entry.data && Object.keys(entry.data).length > 0) {
      const dataStr = JSON.stringify(entry.data, null, 2)
      const coloredData = this.settings.enableColors ? pc.dim(dataStr) : dataStr
      parts.push(coloredData)
    }

    return parts.join('')
  }

  private colorizeLevel(levelName: string, level: LoggerLevel): string {
    switch (level) {
      case LoggerLevel.TRACE:
        return pc.bold(pc.gray(levelName))
      case LoggerLevel.DEBUG:
        return pc.bold(pc.magenta(levelName))
      case LoggerLevel.INFO:
        return pc.bold(pc.blue(levelName))
      case LoggerLevel.SUCCESS:
        return pc.bold(pc.green(levelName))
      case LoggerLevel.WARN:
        return pc.bold(pc.yellow(levelName))
      case LoggerLevel.ERROR:
        return pc.bold(pc.red(levelName))
      default:
        return levelName
    }
  }

  private colorizeMessage(message: string, level: LoggerLevel): string {
    switch (level) {
      case LoggerLevel.TRACE:
        return pc.gray(message)
      case LoggerLevel.DEBUG:
        return pc.blue(message)
      case LoggerLevel.INFO:
        return pc.white(message)
      case LoggerLevel.SUCCESS:
        return pc.green(message)
      case LoggerLevel.WARN:
        return pc.yellow(message)
      case LoggerLevel.ERROR:
        return pc.red(message)
      default:
        return message
    }
  }

  private writeToFile(entry: LogEntry): void {
    if (!this.settings.outputFile) return

    try {
      const logLine = this.formatFileMessage(entry)
      const filePath = join(process.cwd(), 'logs', this.settings.outputFile)

      // Check file size and rotate if necessary
      if (this.fileSize > this.settings.maxFileSize) {
        this.rotateLogFile(filePath)
      }

      appendFileSync(filePath, logLine + '\n', 'utf8')
      this.fileSize += Buffer.byteLength(logLine + '\n', 'utf8') / (1024 * 1024)
    } catch (error) {
      console.error('Failed to write to log file:', error)
    }
  }

  private formatFileMessage(entry: LogEntry): string {
    const parts = [
      entry.timestamp,
      LoggerLevelNames[entry.level],
      entry.context ? `[${entry.context}]` : '',
      this.settings.prefix ? `[${this.settings.prefix}]` : '',
      entry.message,
      entry.data ? JSON.stringify(entry.data) : ''
    ].filter(Boolean)

    return parts.join(' ')
  }

  private rotateLogFile(filePath: string): void {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const rotatedPath = filePath.replace(/\.log$/, `-${timestamp}.log`)

      if (existsSync(filePath)) {
        writeFileSync(rotatedPath, readFileSync(filePath))
        writeFileSync(filePath, '') // Clear the original file
        this.fileSize = 0
      }
    } catch (error) {
      console.error('Failed to rotate log file:', error)
    }
  }

  private maskSensitiveData(data?: LogData): LogData | undefined {
    if (!data) return data

    const sensitiveKeys = ['password', 'token', 'apiKey', 'secret', 'key', 'auth', 'authorization']
    const masked = { ...data }

    for (const key of Object.keys(masked)) {
      if (sensitiveKeys.some((sensitive) => key.toLowerCase().includes(sensitive))) {
        masked[key] = '***MASKED***'
      }
    }

    return masked
  }

  // Utility methods for advanced usage
  public setLevel(level: LoggerLevel): void {
    this.settings.minLevel = level
  }

  public getLevel(): LoggerLevel {
    return this.settings.minLevel
  }

  public getLogs(count?: number): LogEntry[] {
    return count ? this.logBuffer.slice(-count) : [...this.logBuffer]
  }

  public clearLogs(): void {
    this.logBuffer = []
  }

  public updateSettings(newSettings: Partial<LoggerSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    if (newSettings.outputFile) {
      this.initializeFileLogging()
    }
  }
}

export default Logger
