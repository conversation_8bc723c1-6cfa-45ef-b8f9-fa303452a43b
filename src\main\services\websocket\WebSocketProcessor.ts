import { BrowserWindow } from 'electron'
import Logger from '../../../shared/Logger'
import { extractTimePeriod, getChartTimeframe } from '../../../shared/utils/helpers'
import WebSocketDataManager from './WebSocketDataManager'

const logger = new Logger({
  prefix: 'Processor',
  enableColors: true
})

class WebSocketProcessor {
  private static instance: WebSocketProcessor
  private marketData: Map<string, MarketDataResult> = new Map()
  private dataManager: WebSocketDataManager
  private candleHistory: Array<[number, number, number, number, number]> = []
  private currentCandle: {
    timestamp: number
    open: number
    high: number
    low: number
    close: number
    startTime: number
    endTime: number
  } | null = null

  private candleCloseCallbacks: Array<(candle: Candle) => void> = []

  constructor() {
    logger.info(`WebSocketProcessor instance created`)
    this.dataManager = WebSocketDataManager.getInstance()
  }

  public static getInstance(): WebSocketProcessor {
    if (!WebSocketProcessor.instance) {
      WebSocketProcessor.instance = new WebSocketProcessor()
    }
    return WebSocketProcessor.instance
  }

  clearHistory = (): void => {
    this.marketData.clear()
  }

  getMarketData = (): Map<string, MarketDataResult> => {
    return this.marketData
  }

  onCandleClose = (callback: (candle: Candle) => void): void => {
    this.candleCloseCallbacks.push(callback)
  }

  offCandleClose = (): void => {
    this.candleCloseCallbacks = []
  }

  processUpdateHistoryNewFast = (assetSymbol: string, data: HistoryNewFastSettings): void => {
    if (!assetSymbol || !data || data.asset !== assetSymbol) return

    let marketData = this.marketData.get(assetSymbol)
    if (!marketData) {
      marketData = {
        symbol: assetSymbol,
        candles: [],
        latestTick: null,
        lastAnalysis: null
      }

      this.marketData.set(assetSymbol, marketData)
    }

    if (Array.isArray(data.candles)) {
      const newCandles: Candle[] = data.candles.map((candle) => ({
        timestamp: candle[0],
        open: candle[1],
        high: candle[2],
        low: candle[3],
        close: candle[4]
      }))

      newCandles.sort((a, b) => a.timestamp - b.timestamp)

      for (const candle of newCandles) {
        const index = marketData.candles.findIndex((c) => c.timestamp === candle.timestamp)
        if (index >= 0) {
          marketData.candles[index] = candle
        } else {
          marketData.candles.push(candle)
        }
      }

      marketData.candles.sort((a, b) => a.timestamp - b.timestamp)
      if (marketData.candles.length > 500) {
        marketData.candles = marketData.candles.slice(-500)
      }

      logger.info(
        `Processed ${newCandles.length} new candles from updateHistoryNewFast for ${assetSymbol} (Total: ${marketData.candles.length})`
      )
    }

    if (Array.isArray(data.history)) {
      const latestTick = data.history[data.history.length - 1]
      marketData.latestTick = {
        asset: assetSymbol,
        timestamp: latestTick[0],
        price: latestTick[1]
      }
    }

    if (marketData.latestTick) {
      this.dataManager.updateMarketData(
        {
          symbol: assetSymbol,
          price: marketData.latestTick.price
        },
        new Date(marketData.latestTick.timestamp * 1000)
      )
    } else if (marketData.candles.length > 0) {
      const latestCandle = marketData.candles[marketData.candles.length - 1]
      this.dataManager.updateMarketData(
        {
          symbol: assetSymbol,
          price: latestCandle.close
        },
        new Date(latestCandle.timestamp * 1000)
      )
    }
  }

  processLoadHistoryPeriodFast = (assetSymbol: string, data: HistoryPeriodSettings): void => {
    if (!assetSymbol || !data.data || data.asset !== assetSymbol) return

    let marketData = this.marketData.get(assetSymbol)
    if (!marketData) {
      marketData = {
        symbol: assetSymbol,
        candles: [],
        latestTick: null,
        lastAnalysis: null
      }

      this.marketData.set(assetSymbol, marketData)
    }

    // Start processing OHLC data
    if (Array.isArray(data.data)) {
      const historicalCandles: Candle[] = data.data.map((item) => ({
        timestamp: item.time,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close
      }))

      // Sort the historical data
      historicalCandles.sort((a, b) => a.timestamp - b.timestamp)

      // Merge the historical data with the existing data
      for (const candle of historicalCandles) {
        const index = marketData.candles.findIndex((c) => c.timestamp === candle.timestamp)
        if (index >= 0) {
          marketData.candles[index] = candle
        } else {
          marketData.candles.push(candle)
        }
      }

      // Sort the candles and trim to the last 500 candles
      marketData.candles.sort((a, b) => a.timestamp - b.timestamp)
      if (marketData.candles.length > 500) {
        marketData.candles = marketData.candles.slice(-500)
      }

      logger.info(
        `Processed ${historicalCandles.length} historical candles for ${assetSymbol} (Total: ${marketData.candles.length})`
      )

      if (marketData.candles.length > 0) {
        const latestCandle = marketData.candles[marketData.candles.length - 1]
        this.dataManager.updateMarketData(
          {
            symbol: assetSymbol,
            price: latestCandle.close
          },
          new Date(latestCandle.timestamp * 1000)
        )
      }
    }
  }

  processUpdateStream = (
    assetSymbol: string,
    chartSettings: ChartData,
    data: UpstreamSettings
  ): void => {
    if (!assetSymbol || data.length === 0) return

    for (const streamData of data) {
      if (!Array.isArray(streamData) || streamData.length < 3) continue

      const [asset, timestamp, price] = streamData
      const symbol = asset as string

      let marketData = this.marketData.get(symbol)
      if (!marketData) {
        marketData = {
          symbol,
          candles: [],
          latestTick: null,
          lastAnalysis: null
        }

        this.marketData.set(symbol, marketData)
      }

      // Update the latest tick
      marketData.latestTick = {
        asset: symbol,
        timestamp,
        price
      }

      // Update the market data with real-time price
      this.dataManager.updateMarketData(
        {
          symbol,
          price
        },
        new Date(timestamp * 1000)
      )

      this.updateCurrentCandle(marketData, chartSettings, timestamp, price)
    }
  }

  private updateCurrentCandle = (
    marketData: MarketDataResult,
    chartSettings: ChartData,
    timestamp: number,
    price: number
  ): void => {
    const timeframe = getChartTimeframe(chartSettings)
    if (!timeframe) {
      logger.warn(`No timeframe found from chart settings: (Cannot track candles...)`)
    }

    // Convert timeframe to seconds
    const periodToSeconds = extractTimePeriod(timeframe)

    // Calculate candle boundaries
    const candleStartTime = Math.floor(timestamp / periodToSeconds) * periodToSeconds
    const candleEndTime = candleStartTime + periodToSeconds

    // Check if this is a new candle
    if (!this.currentCandle || this.currentCandle.startTime !== candleStartTime) {
      // Previous candle has closed
      if (this.currentCandle) {
        this.onCloseCandle(this.currentCandle)
      }

      // Start a new candle
      this.currentCandle = {
        timestamp: candleStartTime,
        open: price,
        high: price,
        low: price,
        close: price,
        startTime: candleStartTime,
        endTime: candleEndTime
      }

      marketData.candles.push(this.currentCandle)

      // Broadcast the next candle close time for the countdown timer
      this.broadcast('candle:started', {
        candle: this.currentCandle,
        nextCloseTime: candleEndTime * 1000 // Convert to milliseconds
      })
    } else {
      this.currentCandle.high = Math.max(this.currentCandle.high, price)
      this.currentCandle.low = Math.min(this.currentCandle.low, price)
      this.currentCandle.close = price
    }

    marketData.candles.sort((a, b) => a.timestamp - b.timestamp)
    if (marketData.candles.length > 500) {
      marketData.candles = marketData.candles.slice(-500)
    }
  }

  private onCloseCandle = (candle: typeof this.currentCandle): void => {
    if (!candle) return
    const existingCandleIndex = this.candleHistory.findIndex((c) => c[0] === candle.timestamp)

    if (existingCandleIndex !== -1) {
      // Update the existing candle
      this.candleHistory[existingCandleIndex] = [
        candle.timestamp,
        candle.open,
        candle.high,
        candle.low,
        candle.close
      ]
    } else {
      this.candleHistory.push([
        candle.timestamp,
        candle.open,
        candle.high,
        candle.low,
        candle.close
      ])

      // Keep only the last 500 candles
      if (this.candleHistory.length > 500) {
        this.candleHistory.shift()
      }
    }

    // Create a Candle object for callbacks
    const closedCandle: Candle = {
      timestamp: candle.timestamp,
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close
    }

    // Notify all registered callbacks
    this.candleCloseCallbacks.forEach((callback) => {
      try {
        callback(closedCandle)
      } catch (error) {
        logger.error(`Error in candle close callback: ${error}`)
      }
    })

    // Calculate the next candle close time
    if (candle.endTime) {
      this.broadcast('candle:closed', {
        candle: closedCandle,
        nextCloseTime: candle.endTime * 1000
      })
    }
  }

  private broadcast = (event: string, data?: unknown): void => {
    BrowserWindow.getAllWindows().forEach((win) => {
      if (win && !win.isDestroyed()) {
        if (data) {
          win.webContents.send('ws:event', event, data)
        } else {
          win.webContents.send('ws:event', event)
        }
      }
    })
  }
}

export default WebSocketProcessor
