class RSIIndicator {
  private static instance: RSIIndicator
  private settings: RSISettings
  private priceHistory: number[] = []
  private lastRSI: number | null = null

  constructor(settings: Partial<RSISettings> = {}) {
    this.settings = {
      ...settings,
      period: 14,
      overboughtLevel: 70,
      oversoldLevel: 30
    }
  }

  public static getInstance(): RSIIndicator {
    if (!RSIIndicator.instance) {
      RSIIndicator.instance = new RSIIndicator()
    }
    return RSIIndicator.instance
  }

  calculate(prices: number[]): RSIResult | null {
    if (prices.length <= this.settings.period + 1) return null

    // Calculate price changes
    const changes = this.calculatePriceChange(prices)

    // Separate gains and losses
    const gains = changes.map((change) => (change > 0 ? change : 0))
    const losses = changes.map((change) => (change < 0 ? Math.abs(change) : 0))

    // Calculate average gain and loss
    const avgGain = this.calculateAverage(gains.slice(-this.settings.period))
    const avgLoss = this.calculateAverage(losses.slice(-this.settings.period))

    // Calculate RSI
    if (avgLoss === 0) {
      return {
        value: 100,
        signal: 'SELL',
        strength: 'STRONG',
        timestamp: new Date()
      }
    }

    const rs = avgGain / avgLoss
    const rsi = 100 - 100 / (1 + rs)

    this.lastRSI = rsi

    return {
      value: Number(rsi.toFixed(2)),
      signal: this.generateSignal(rsi),
      strength: this.calculateStrength(rsi),
      timestamp: new Date()
    }
  }

  update(newPrice: number): RSIResult | null {
    this.priceHistory.push(newPrice)

    // Keep only necessary history
    if (this.priceHistory.length > this.settings.period + 50) {
      this.priceHistory = this.priceHistory.slice(-this.settings.period - 20)
    }

    return this.calculate(this.priceHistory)
  }

  getSettings(): RSISettings {
    return this.settings
  }

  updateSettings(newSettings: Partial<RSISettings>): void {
    this.settings = {
      ...this.settings,
      ...newSettings
    }
  }

  getLastValue(): number | null {
    return this.lastRSI
  }

  reset(): void {
    this.priceHistory = []
    this.lastRSI = null
  }

  checkDivergence(
    prices: number[],
    rsiValues: number[]
  ): { bullish: boolean; bearish: boolean } | null {
    if (prices.length < 10 || rsiValues.length < 10) return { bullish: false, bearish: false }

    const recentPrices = prices.slice(-5)
    const recentRSI = rsiValues.slice(-5)

    const priceUptrend = recentPrices[4] > recentPrices[0]
    const priceDowntrend = recentPrices[4] < recentPrices[0]

    const rsiUptrend = recentRSI[4] > recentRSI[0]
    const rsiDowntrend = recentRSI[4] < recentRSI[0]

    return {
      bullish: priceDowntrend && rsiUptrend,
      bearish: priceUptrend && rsiDowntrend
    }
  }

  private calculateStrength = (rsi: number): 'WEAK' | 'MODERATE' | 'STRONG' => {
    const distanceFromOverbought = Math.abs(rsi - this.settings.overboughtLevel)
    const distanceFromOversold = Math.abs(rsi - this.settings.oversoldLevel)
    const minDistance = Math.min(distanceFromOverbought, distanceFromOversold)

    if (minDistance <= 5) {
      return 'STRONG'
    } else if (minDistance <= 15) {
      return 'MODERATE'
    } else {
      return 'WEAK'
    }
  }

  private generateSignal = (rsi: number): 'BUY' | 'SELL' | 'NEUTRAL' => {
    if (rsi <= this.settings.oversoldLevel) {
      return 'BUY'
    } else if (rsi >= this.settings.overboughtLevel) {
      return 'SELL'
    } else {
      return 'NEUTRAL'
    }
  }

  private calculateAverage = (values: number[]): number => {
    if (values.length === 0) return 0
    return values.reduce((acc, value) => acc + value, 0) / values.length
  }

  private calculatePriceChange = (prices: number[]): number[] => {
    const changes: number[] = []
    for (let i = 1; i < prices.length; i++) {
      changes.push(prices[i] - prices[i - 1])
    }

    return changes
  }
}

export default RSIIndicator
