import TechnicalAnalysis, { TechnicalAnalysisResult } from './TechnicalAnalysis'

interface StrategySettings {
  rsiOversold: number
  rsiOverbought: number
  riskPerTrade: number
}

class StrategyManager {
  private technicalAnalysis: TechnicalAnalysis
  private currentStrategy: string
  private settings: StrategySettings

  constructor() {
    this.technicalAnalysis = TechnicalAnalysis.getInstance()
    this.currentStrategy = 'rsi-bollinger'
    this.settings = {
      rsiOversold: 30,
      rsiOverbought: 70,
      riskPerTrade: 2
    }
  }

  analyze(marketData: MarketData[]): StrategyResult | null {
    if (marketData.length <= 20) return null

    // Run technical analysis with current strategy settings
    const technicalAnalysis = this.technicalAnalysis.analyze(marketData)
    if (!technicalAnalysis) return null

    const signals = this.generateStrategySignals(technicalAnalysis, marketData)
    const primarySignal = this.determinePrimarySignal(signals)

    const result: StrategyResult = {
      signals,
      primarySignal,
      confidence: this.calculateOverallConfidence(signals),
      shouldTrade: this.shouldExecuteTrade(primarySignal),
      recommendedAmount: this.calculateRecommendedAmount(primarySignal),
      recommendedExpiration: this.calculateRecommendedExpiration(primarySignal)
    }

    return result
  }

  private generateStrategySignals(
    technicalAnalysis: TechnicalAnalysisResult,
    marketData: MarketData[]
  ): StrategySignal[] {
    const signals: StrategySignal[] = []
    const currentData = marketData[marketData.length - 1]

    switch (this.currentStrategy) {
      case 'rsi-bollinger':
        signals.push(this.generateRSIBollingerSignal(technicalAnalysis, currentData))
        break
      case 'ma-crossover':
        signals.push(this.generateMACrossoverSignal(technicalAnalysis, currentData))
        break
      case 'breakout':
        signals.push(this.generateBreakoutSignal(technicalAnalysis, currentData))
        break
      case 'momentum':
        signals.push(this.generateMomentumSignal(technicalAnalysis, currentData))
    }

    return signals.filter((signal) => signal !== null)
  }

  private generateRSIBollingerSignal(
    analysis: TechnicalAnalysisResult,
    currentData: MarketData
  ): StrategySignal {
    const reasoning: string[] = []
    let signal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let confidence = 0
    let strength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG' = 'WEAK'

    if (analysis.rsi && analysis.bollingerBands) {
      // RSI oversold + near lower Bollinger Band = BUY
      if (
        analysis.rsi.value <= this.settings.rsiOversold &&
        analysis.bollingerBands.percentB <= 0.1
      ) {
        signal = 'BUY'
        confidence = 0.8
        strength = 'STRONG'
        reasoning.push(`RSI oversold (${analysis.rsi.value.toFixed(1)})`)
        reasoning.push(
          `Price near lower Bollinger Band (${(analysis.bollingerBands.percentB * 100).toFixed(1)}%)`
        )
      }
      // RSI overbought + near upper Bollinger Band = SELL
      else if (
        analysis.rsi.value >= this.settings.rsiOverbought &&
        analysis.bollingerBands.percentB >= 0.9
      ) {
        signal = 'SELL'
        confidence = 0.8
        strength = 'STRONG'
        reasoning.push(`RSI overbought (${analysis.rsi.value.toFixed(1)})`)
        reasoning.push(
          `Price near upper Bollinger Band (${(analysis.bollingerBands.percentB * 100).toFixed(1)}%)`
        )
      }
      // Moderate signals
      else if (analysis.rsi.value <= this.settings.rsiOversold) {
        signal = 'BUY'
        confidence = 0.6
        strength = 'MODERATE'
        reasoning.push(`RSI oversold (${analysis.rsi.value.toFixed(1)})`)
      } else if (analysis.rsi.value >= this.settings.rsiOverbought) {
        signal = 'SELL'
        confidence = 0.6
        strength = 'MODERATE'
        reasoning.push(`RSI overbought (${analysis.rsi.value.toFixed(1)})`)
      }
    }

    return {
      strategy: 'rsi-bollinger',
      signal,
      confidence,
      strength,
      timestamp: new Date(),
      symbol: currentData.symbol,
      currentPrice: currentData.price,
      reasoning,
      entryConditions: reasoning
    }
  }

  private generateMACrossoverSignal(
    analysis: TechnicalAnalysisResult,
    currentData: MarketData
  ): StrategySignal {
    const reasoning: string[] = []
    let signal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let confidence = 0
    let strength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG' = 'WEAK'

    if (analysis.movingAverage) {
      const shortMA = analysis.movingAverage.shortSMA
      const longMA = analysis.movingAverage.longSMA
      const priceTrend = analysis.trend
      const crossoverType = analysis.movingAverage.crossoverType

      // Actual crossover detection takes priority
      if (crossoverType === 'GOLDEN_CROSS') {
        signal = 'BUY'
        confidence = 0.85
        strength = 'VERY_STRONG'
        reasoning.push(`Golden Cross detected: Short MA crossed above Long MA`)
        reasoning.push(`Short MA: ${shortMA.toFixed(5)}, Long MA: ${longMA.toFixed(5)}`)
      } else if (crossoverType === 'DEATH_CROSS') {
        signal = 'SELL'
        confidence = 0.85
        strength = 'VERY_STRONG'
        reasoning.push(`Death Cross detected: Short MA crossed below Long MA`)
        reasoning.push(`Short MA: ${shortMA.toFixed(5)}, Long MA: ${longMA.toFixed(5)}`)
      }
      // Check for MA positioning and trend alignment
      else if (shortMA > longMA) {
        const separation = ((shortMA - longMA) / longMA) * 100
        if (separation > 0.01) {
          signal = 'BUY'
          confidence = Math.min(0.7, 0.4 + separation * 10) // Scale confidence with separation
          strength = separation > 0.05 ? 'STRONG' : 'MODERATE'
          reasoning.push(`Short MA above Long MA (${separation.toFixed(3)}% separation)`)
          if (priceTrend === 'UPTREND' || priceTrend === 'STRONG_UPTREND') {
            confidence += 0.1
            reasoning.push('Trend alignment: Bullish trend confirmed')
          }
        }
      } else if (shortMA < longMA) {
        const separation = ((longMA - shortMA) / longMA) * 100
        if (separation > 0.01) {
          signal = 'SELL'
          confidence = Math.min(0.7, 0.4 + separation * 10) // Scale confidence with separation
          strength = separation > 0.05 ? 'STRONG' : 'MODERATE'
          reasoning.push(`Short MA below Long MA (${separation.toFixed(3)}% separation)`)
          if (priceTrend === 'DOWNTREND' || priceTrend === 'STRONG_DOWNTREND') {
            confidence += 0.1
            reasoning.push('Trend alignment: Bearish trend confirmed')
          }
        }
      }

      // Add MA trend analysis
      if (reasoning.length === 0) {
        reasoning.push(
          `MAs converging: Short MA ${shortMA.toFixed(5)}, Long MA ${longMA.toFixed(5)}`
        )
        reasoning.push('Waiting for clearer directional signal')
      }
    }

    return {
      strategy: 'ma-crossover',
      signal,
      confidence,
      strength,
      timestamp: new Date(),
      symbol: currentData.symbol,
      currentPrice: currentData.price,
      reasoning,
      entryConditions: reasoning
    }
  }

  private generateBreakoutSignal(
    analysis: TechnicalAnalysisResult,
    currentData: MarketData
  ): StrategySignal {
    const reasoning: string[] = []
    let signal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let confidence = 0
    let strength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG' = 'WEAK'

    if (analysis.bollingerBands && analysis.supportLevel && analysis.resistanceLevel) {
      const price = currentData.price
      const upperBand = analysis.bollingerBands.upperBand
      const lowerBand = analysis.bollingerBands.lowerBand
      const resistance = analysis.resistanceLevel
      const support = analysis.supportLevel

      // Bullish breakout above resistance and upper Bollinger Band
      if (price > resistance && price > upperBand) {
        signal = 'BUY'
        confidence = 0.85
        strength = 'VERY_STRONG'
        reasoning.push(`Bullish breakout above resistance (${resistance.toFixed(5)})`)
        reasoning.push(`Price above upper Bollinger Band (${upperBand.toFixed(5)})`)
      }
      // Bearish breakdown below support and lower Bollinger Band
      else if (price < support && price < lowerBand) {
        signal = 'SELL'
        confidence = 0.85
        strength = 'VERY_STRONG'
        reasoning.push(`Bearish breakdown below support (${support.toFixed(5)})`)
        reasoning.push(`Price below lower Bollinger Band (${lowerBand.toFixed(5)})`)
      }
      // Approaching resistance
      else if (price > resistance * 0.998) {
        signal = 'BUY'
        confidence = 0.6
        strength = 'MODERATE'
        reasoning.push(`Price approaching resistance (${resistance.toFixed(5)})`)
      }
      // Approaching support
      else if (price < support * 1.002) {
        signal = 'SELL'
        confidence = 0.6
        strength = 'MODERATE'
        reasoning.push(`Price approaching support (${support.toFixed(5)})`)
      }
    }

    return {
      strategy: 'breakout',
      signal,
      confidence,
      strength,
      timestamp: new Date(),
      symbol: currentData.symbol,
      currentPrice: currentData.price,
      reasoning,
      entryConditions: reasoning
    }
  }

  private generateMomentumSignal(
    analysis: TechnicalAnalysisResult,
    currentData: MarketData
  ): StrategySignal {
    const reasoning: string[] = []
    let signal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let confidence = 0
    let strength: 'WEAK' | 'MODERATE' | 'STRONG' | 'VERY_STRONG' = 'WEAK'

    if (analysis.rsi && analysis.macd) {
      const rsi = analysis.rsi.value
      const macdLine = analysis.macd.macd
      const signalLine = analysis.macd.signal
      const histogram = analysis.macd.histogram

      // Strong bullish momentum
      if (
        rsi > 50 &&
        macdLine > signalLine &&
        histogram > 0 &&
        analysis.macd.crossover === 'BULLISH'
      ) {
        signal = 'BUY'
        confidence = 0.8
        strength = 'STRONG'
        reasoning.push(`RSI above 50 (${rsi.toFixed(1)}) - bullish momentum`)
        reasoning.push(`MACD bullish crossover`)
        reasoning.push(`Positive histogram (${histogram.toFixed(4)})`)
      }
      // Strong bearish momentum
      else if (
        rsi < 50 &&
        macdLine < signalLine &&
        histogram < 0 &&
        analysis.macd.crossover === 'BEARISH'
      ) {
        signal = 'SELL'
        confidence = 0.8
        strength = 'STRONG'
        reasoning.push(`RSI below 50 (${rsi.toFixed(1)}) - bearish momentum`)
        reasoning.push(`MACD bearish crossover`)
        reasoning.push(`Negative histogram (${histogram.toFixed(4)})`)
      }
      // Moderate momentum signals
      else if (macdLine > signalLine && histogram > 0) {
        signal = 'BUY'
        confidence = 0.6
        strength = 'MODERATE'
        reasoning.push(`MACD above signal line`)
        reasoning.push(`Positive momentum`)
      } else if (macdLine < signalLine && histogram < 0) {
        signal = 'SELL'
        confidence = 0.6
        strength = 'MODERATE'
        reasoning.push(`MACD below signal line`)
        reasoning.push(`Negative momentum`)
      }
    }

    return {
      strategy: 'momentum',
      signal,
      confidence,
      strength,
      timestamp: new Date(),
      symbol: currentData.symbol,
      currentPrice: currentData.price,
      reasoning,
      entryConditions: reasoning
    }
  }

  /**
   * Determine the primary signal from all generated signals
   */
  private determinePrimarySignal(signals: StrategySignal[]): StrategySignal | null {
    if (signals.length === 0) {
      return null
    }

    // Sort by confidence and strength
    const sortedSignals = signals.sort((a, b) => {
      const strengthScore = { WEAK: 1, MODERATE: 2, STRONG: 3, VERY_STRONG: 4 }
      const aScore = a.confidence * strengthScore[a.strength]
      const bScore = b.confidence * strengthScore[b.strength]
      return bScore - aScore
    })

    return sortedSignals[0]
  }

  /**
   * Calculate overall confidence from all signals
   */
  private calculateOverallConfidence(signals: StrategySignal[]): number {
    if (signals.length === 0) {
      return 0
    }

    const totalConfidence = signals.reduce((sum, signal) => sum + signal.confidence, 0)
    return totalConfidence / signals.length
  }

  /**
   * Determine if a trade should be executed
   */
  private shouldExecuteTrade(signal: StrategySignal | null): boolean {
    if (!signal || signal.signal === 'NEUTRAL') {
      return false
    }

    // Minimum confidence and strength requirements
    const minConfidence = 0.6
    const minStrengthRequired = ['MODERATE', 'STRONG', 'VERY_STRONG']

    return signal.confidence >= minConfidence && minStrengthRequired.includes(signal.strength)
  }

  /**
   * Calculate recommended trade amount based on signal strength and risk settings
   */
  private calculateRecommendedAmount(signal: StrategySignal | null): number | undefined {
    if (!signal || !this.shouldExecuteTrade(signal)) {
      return undefined
    }

    // Base amount calculation based on risk per trade
    const baseAmount = 100 // Default base amount
    const riskMultiplier = this.settings.riskPerTrade / 100

    let strengthMultiplier = 1
    switch (signal.strength) {
      case 'VERY_STRONG':
        strengthMultiplier = 1.5
        break
      case 'STRONG':
        strengthMultiplier = 1.2
        break
      case 'MODERATE':
        strengthMultiplier = 1.0
        break
      case 'WEAK':
        strengthMultiplier = 0.5
        break
    }

    const recommendedAmount = baseAmount * riskMultiplier * strengthMultiplier * signal.confidence

    // Ensure within reasonable bounds
    return Math.max(10, Math.min(recommendedAmount, 1000))
  }

  /**
   * Calculate recommended expiration time based on strategy and signal strength
   */
  private calculateRecommendedExpiration(signal: StrategySignal | null): number | undefined {
    if (!signal || !this.shouldExecuteTrade(signal)) {
      return undefined
    }

    // Base expiration times by strategy
    const baseExpiration: Record<StrategyType, number> = {
      'rsi-bollinger': 60, // 1 minute
      'ma-crossover': 180, // 3 minutes
      breakout: 300, // 5 minutes
      momentum: 120 // 2 minutes
    }

    let expiration = baseExpiration[signal.strategy]

    // Adjust based on signal strength
    switch (signal.strength) {
      case 'VERY_STRONG':
        expiration *= 1.5
        break
      case 'STRONG':
        expiration *= 1.2
        break
      case 'MODERATE':
        expiration *= 1.0
        break
      case 'WEAK':
        expiration *= 0.8
        break
    }

    // Ensure within PocketOption bounds (30s - 600s typically)
    return Math.max(30, Math.min(Math.round(expiration), 600))
  }

  /**
   * Reset the strategy manager
   */
  reset(): void {
    this.technicalAnalysis.reset()
  }
}

export default StrategyManager
