import Logger from '../../shared/Logger'

const logger = new Logger({
  prefix: 'RISK',
  enableColors: true
})

class RiskManager {
  private settings: RiskSettings
  private sessionStats: SessionStats

  constructor(settings: Partial<RiskSettings> = {}) {
    this.settings = {
      // Capital Management
      maxDailyRisk: settings.maxDailyRisk || 2, // 2% of balance
      riskPerTrade: settings.riskPerTrade || 1, // 1% of balance per trade
      maxConcurrentTrades: settings.maxConcurrentTrades || 3,
      maxExposurePerAsset: settings.maxExposurePerAsset || 5, // 5% of balance

      // Profit/Loss Management
      dailyProfitTarget: settings.dailyProfitTarget || 100,
      dailyLossLimit: settings.dailyLossLimit || 50,
      sessionProfitTarget: settings.sessionProfitTarget || 50,
      sessionLossLimit: settings.sessionLossLimit || 25,

      // Stacking/Martingale
      enableMartingale: settings.enableMartingale || false,
      martingaleMultiplier: settings.martingaleMultiplier || 2.0,
      maxMartingaleSteps: settings.maxMartingaleSteps || 3,
      martingaleResetOnWin: settings.martingaleResetOnWin || true,

      // Time Management
      minTimeBetweenTrades: settings.minTimeBetweenTrades || 30, // 30 seconds
      cooldownAfterLoss: settings.cooldownAfterLoss || 60, // 60 seconds
      respectExpirationTimes: settings.respectExpirationTimes || true,
      maxTradesPerHour: settings.maxTradesPerHour || 20,

      // Position Sizing
      minTradeAmount: settings.minTradeAmount || 10,
      maxTradeAmount: settings.maxTradeAmount || 100,
      dynamicSizing: settings.dynamicSizing || false
    }

    this.sessionStats = this.createDefaultSessionStats()
  }

  initializeSession(balance: number): void {
    this.sessionStats = {
      ...this.createDefaultSessionStats(),
      startBalance: balance,
      currentBalance: balance
    }
    logger.info(`Risk management session initialized with balance: $${balance}`)
  }

  async canTrade(request: TradeRequest): Promise<boolean> {
    // Check daily loss limit
    if (this.sessionStats.dailyProfit <= -this.settings.dailyLossLimit) {
      logger.warn('Daily loss limit reached')
      return false
    }

    // Check session loss limit
    if (this.sessionStats.totalProfit <= -this.settings.sessionLossLimit) {
      logger.warn('Session loss limit reached')
      return false
    }

    // Check concurrent trades
    if (this.sessionStats.totalTrades >= this.settings.maxConcurrentTrades) {
      logger.warn('Max concurrent trades reached')
      return false
    }

    // Check time between trades
    if (this.sessionStats.lastTradeTime) {
      const timeSinceLastTrade = Date.now() - this.sessionStats.lastTradeTime.getTime()
      const minTime =
        this.sessionStats.consecutiveLosses > 0
          ? this.settings.cooldownAfterLoss * 1000
          : this.settings.minTimeBetweenTrades * 1000

      if (timeSinceLastTrade < minTime) {
        logger.warn(`Need to wait ${(minTime - timeSinceLastTrade) / 1000}s before next trade`)
        return false
      }
    }

    // Check hourly trade limit
    const now = new Date()
    if (this.sessionStats.hourlyTradeReset < now) {
      this.sessionStats.tradesThisHour = 0
      this.sessionStats.hourlyTradeReset = new Date(now.getTime() + 3600000) // 1 hour
    }

    if (this.sessionStats.tradesThisHour >= this.settings.maxTradesPerHour) {
      logger.warn('Max trades per hour reached')
      return false
    }

    // Check trade amount limits
    if (
      request.amount < this.settings.minTradeAmount ||
      request.amount > this.settings.maxTradeAmount
    ) {
      logger.warn(`Trade amount ${request.amount} outside allowed range`)
      return false
    }

    return true
  }

  getTradeAmount(): number {
    let amount = this.settings.minTradeAmount

    if (this.settings.dynamicSizing && this.sessionStats.currentBalance > 0) {
      // Calculate amount based on risk per trade
      amount = this.sessionStats.currentBalance * (this.settings.riskPerTrade / 100)

      // Apply martingale if enabled
      if (this.settings.enableMartingale && this.sessionStats.consecutiveLosses > 0) {
        const martingaleStep = Math.min(
          this.sessionStats.consecutiveLosses,
          this.settings.maxMartingaleSteps
        )
        amount *= Math.pow(this.settings.martingaleMultiplier, martingaleStep)
      }
    }

    // Ensure within limits
    return Math.max(this.settings.minTradeAmount, Math.min(amount, this.settings.maxTradeAmount))
  }

  updateTradeResult(won: boolean, profit: number): void {
    this.sessionStats.totalTrades++
    this.sessionStats.tradesThisHour++
    this.sessionStats.lastTradeTime = new Date()

    if (won) {
      this.sessionStats.winTrades++
      this.sessionStats.consecutiveWins++
      this.sessionStats.consecutiveLosses = 0

      if (this.settings.martingaleResetOnWin) {
        // Reset martingale on win
        this.sessionStats.consecutiveLosses = 0
      }
    } else {
      this.sessionStats.lossTrades++
      this.sessionStats.consecutiveLosses++
      this.sessionStats.consecutiveWins = 0
    }

    this.sessionStats.totalProfit += profit
    this.sessionStats.dailyProfit += profit
    this.sessionStats.currentBalance += profit
    this.sessionStats.winRate = (this.sessionStats.winTrades / this.sessionStats.totalTrades) * 100

    logger.info(
      `Trade result: ${won ? 'WIN' : 'LOSS'}, Profit: $${profit.toFixed(2)}, Win Rate: ${this.sessionStats.winRate.toFixed(1)}%`
    )
  }

  getSessionStats(): SessionStats {
    return { ...this.sessionStats }
  }

  resetDailyStats(): void {
    this.sessionStats.dailyProfit = 0
    logger.info('Daily stats reset')
  }

  private createDefaultSessionStats(): SessionStats {
    return {
      startBalance: 0,
      currentBalance: 0,
      totalTrades: 0,
      winTrades: 0,
      lossTrades: 0,
      drawTrades: 0,
      totalProfit: 0,
      dailyProfit: 0,
      winRate: 0,
      lastTradeTime: null,
      consecutiveLosses: 0,
      consecutiveWins: 0,
      tradesThisHour: 0,
      hourlyTradeReset: new Date(Date.now() + 3600000)
    }
  }
}

export default RiskManager
