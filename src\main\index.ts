import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import Logger from '../shared/Logger'
import WebSocketClient from './services/websocket/WebSocketClient'
import WebSocketDataManager from './services/websocket/WebSocketDataManager'
import AutoTrader from './services/AutoTrader'

const logger = new Logger({
  outputFile: 'main.log',
  prefix: 'MAIN',
  enableColors: true
})

const dataManager = WebSocketDataManager.getInstance()
const autoTrader = AutoTrader.getInstance()

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  const wsClient = WebSocketClient.getInstance()

  // Initialize AutoTrader with WebSocket client
  autoTrader.initialize(wsClient)

  // IPC test
  ipcMain.on('ping', () => {
    logger.info(`Received ping from renderer`)
  })

  ipcMain.on('ws:selectAsset', (_, data: string) => {
    wsClient.setAsset(data)
  })

  /**
   * Send logs to renderer
   */
  ipcMain.on('event:log', (_, message) => {
    logger.info(`Received log event from renderer: ${message}`)
    BrowserWindow.getAllWindows().forEach((win) => {
      if (win && !win.isDestroyed()) {
        // Sends the log message back to the renderer
        win.webContents.send('event:log', message)
      }
    })
  })

  ipcMain.handle('ws:subscribe', (_, data: DataSubscription) => {
    dataManager.subscribe(data)
  })

  // Bot control handlers
  ipcMain.on('bot:start', () => {
    logger.info('Starting bot from UI')
    autoTrader.start()
  })

  ipcMain.on('bot:stop', () => {
    logger.info('Stopping bot from UI')
    autoTrader.stop()
  })

  ipcMain.on('bot:updateSettings', (_, settings) => {
    logger.info('Updating bot settings from UI')
    autoTrader.updateSettings(settings)
  })

  ipcMain.handle('bot:getStatus', () => {
    return {
      isEnabled: autoTrader.isEnabled(),
      settings: autoTrader.getSettings(),
      session: autoTrader.getSessionStats()
    }
  })

  ipcMain.on('bot:triggerAnalysis', () => {
    logger.info('Manual analysis triggered from UI')
    autoTrader.triggerAnalysis()
  })

  ipcMain.on('bot:setInterval', (_, intervalMs: number) => {
    logger.info(`Setting analysis interval to ${intervalMs}ms`)
    autoTrader.setAnalysisInterval(intervalMs)
  })

  createWindow()

  app.on('activate', function () {
    logger.info(`App is active`)
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
