// type HistoryPeriodData = Pick<HistoryData, 'asset' | 'data' | 'period'>
type HistoryPeriodCandle = {
  symbol_id: number
  time: number
  open: number
  close: number
  high: number
  low: number
  asset: string
}

// interface HistoryPeriodData {
//   asset: string
//   data: HistoryPeriodCandle[]
//   period: number
// }

interface HistoryPeriodSettings {
  asset: string
  index: number
  data: HistoryPeriodCandle[]
  period: number
}
